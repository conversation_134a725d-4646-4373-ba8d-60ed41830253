#!/usr/bin/env python3
import random
import os
import argparse

"""
Wordlist Generator for AutoBeef

This script generates a wordlist of aggressive/trash-talking phrases
that can be used with the AutoBeef feature in Zephcord.
"""

# Base components for generating phrases
# Start with empty lists that can be populated through the interface
SUBJECTS = []
INSULTS = []
RELATIONS = []
ACTIONS = []
INTENSIFIERS = []
DISMISSALS = []
EXPLETIVES = []

# Add some initial examples to each category
if not SUBJECTS:
    SUBJECTS = ["U", "UR"]

if not INSULTS:
    INSULTS = ["WEAK", "UGLY", "SLOW"]

if not RELATIONS:
    RELATIONS = ["SON", "BITCH", "SLAVE"]

if not ACTIONS:
    ACTIONS = ["SHUT UP", "FOCUS", "KILL URSELF"]

if not INTENSIFIERS:
    INTENSIFIERS = ["FUCKING", "ASS", "VERY"]

if not DISMISSALS:
    DISMISSALS = ["SADLY", "TO", "AND", "AS FUCK"]

def generate_phrase():
    """Generate a random trash talk phrase"""
    # Sometimes generate a completely custom aggressive phrase
    if random.random() < 0.2:  # 20% chance for custom aggressive phrase
        return generate_aggressive_phrase()
    
    template = random.choice(TEMPLATES)
    
    # Replace placeholders with random choices from the corresponding lists
    phrase = template
    if "{subject}" in phrase:
        phrase = phrase.replace("{subject}", random.choice(SUBJECTS))
    if "{verb}" in phrase:
        phrase = phrase.replace("{verb}", random.choice(VERBS))
    if "{adjective}" in phrase:
        phrase = phrase.replace("{adjective}", random.choice(INSULTS))
    if "{relation}" in phrase:
        phrase = phrase.replace("{relation}", random.choice(RELATIONS))
    if "{action}" in phrase:
        phrase = phrase.replace("{action}", random.choice(ACTIONS))
    if "{intensifier}" in phrase:
        phrase = phrase.replace("{intensifier}", random.choice(INTENSIFIERS))
    if "{dismissal}" in phrase:
        phrase = phrase.replace("{dismissal}", random.choice(DISMISSALS))
    if "{expletive}" in phrase:
        phrase = phrase.replace("{expletive}", random.choice(EXPLETIVES))
    
    return phrase

def generate_aggressive_phrase():
    """Generate a more complex aggressive phrase similar to the examples"""
    # Define patterns based on the examples
    patterns = [
        # Simple insults
        lambda: f"{random.choice(INSULTS)} {random.choice(INSULTS)} {random.choice(EXPLETIVES)}",
        lambda: f"{random.choice(INSULTS)} {random.choice(EXPLETIVES)}",
        lambda: f"{random.choice(EXPLETIVES)} {random.choice(INSULTS)}",
        lambda: f"SHIT BAG",
        lambda: f"{random.choice(INSULTS)} {random.choice(INSULTS)} {random.choice(INSULTS)}",
        
        # Action statements
        lambda: f"I DROWNED U IN WATER",
        lambda: f"I HUNT U DAILY",
        lambda: f"IM A THREAT AROUND U",
        lambda: f"ILL TEAR UR SKIN OPEN",
        lambda: f"KILL URSELF",
        lambda: f"RETHINK UR LIFE",
        lambda: f"BEG FOR MERCY",
        lambda: f"KEEP UR EYES OPEN",
        lambda: f"PISS URSELF",
        lambda: f"FOCUS ON ME",
        lambda: f"FOCUS",
        
        # Subject-based insults
        lambda: f"{random.choice(SUBJECTS)} {random.choice(VERBS)} {random.choice(INSULTS)}",
        lambda: f"{random.choice(SUBJECTS)} {random.choice(VERBS)} {random.choice(INSULTS)} {random.choice(DISMISSALS)}",
        lambda: f"{random.choice(DISMISSALS)} {random.choice(SUBJECTS)} {random.choice(VERBS)} {random.choice(INSULTS)}",
        lambda: f"{random.choice(SUBJECTS)} {random.choice(VERBS)} {random.choice(INSULTS)} {random.choice(INTENSIFIERS)}",
        lambda: f"{random.choice(SUBJECTS)} {random.choice(VERBS)} {random.choice(EXPLETIVES)} {random.choice(INSULTS)}",
        
        # Ownership statements
        lambda: f"UR MY {random.choice(RELATIONS)}",
        lambda: f"UR MY {random.choice(EXPLETIVES)} {random.choice(RELATIONS)}",
        lambda: f"UR MY BTICH",
        lambda: f"BOY IS MY BITCH",
        
        # Statements with "to"
        lambda: f"UR TO {random.choice(INSULTS)} {random.choice(DISMISSALS)}",
        lambda: f"UR TO {random.choice(INSULTS)}",
        lambda: f"UR TO {random.choice(INSULTS)} AND A {random.choice(INSULTS)}",
        lambda: f"UR TO BALD SON",
        
        # Statements with "sadly"
        lambda: f"{random.choice(DISMISSALS)} I DONT ADMIRE U",
        lambda: f"{random.choice(DISMISSALS)} UR {random.choice(INSULTS)}",
        lambda: f"{random.choice(DISMISSALS)} UR A {random.choice(INSULTS)}",
        lambda: f"{random.choice(DISMISSALS)} IM A GOD",
        lambda: f"NOBODY ADMIRES U {random.choice(DISMISSALS)}",
        
        # Statements with "and"
        lambda: f"UR {random.choice(INSULTS)} AND UR {random.choice(INSULTS)}",
        lambda: f"UR {random.choice(INSULTS)} AND UR SHIT TO ME",
        lambda: f"UR {random.choice(EXPLETIVES)} {random.choice(INSULTS)} AND UR {random.choice(INSULTS)}",
        
        # Statements with "as fuck"
        lambda: f"{random.choice(SUBJECTS)} {random.choice(VERBS)} {random.choice(INSULTS)} AS FUCK",
        lambda: f"{random.choice(SUBJECTS)} {random.choice(VERBS)} SPED AS FUCK",
        lambda: f"{random.choice(SUBJECTS)} {random.choice(VERBS)} DIRTY AS FUCK",
        lambda: f"{random.choice(SUBJECTS)} COST 1 AND IS SLOW AS FUCK",
        lambda: f"UR POOR AS FUCK",
        lambda: f"JEW IS HORRID AS FUCK",
        
        # Death/failure statements
        lambda: f"{random.choice(SUBJECTS)} DIED",
        lambda: f"{random.choice(SUBJECTS)} FAILED TO SUICIDE",
        lambda: f"{random.choice(SUBJECTS)} STABBED HIMSELF",
        lambda: f"{random.choice(SUBJECTS)} SHITS ON HIMSELF",
        lambda: f"{random.choice(SUBJECTS)} WORSHIPS GAY FAGGOTS",
        lambda: f"{random.choice(SUBJECTS)} WORSHIPS DOG FECES",
        lambda: f"{random.choice(SUBJECTS)} STALKS KIDS",
        lambda: f"{random.choice(SUBJECTS)} LEECHS OFF ME",
        lambda: f"{random.choice(SUBJECTS)} CANT FOCUS",
        lambda: f"SAD REJECT DIED",
        lambda: f"STUPID FAGGOT DIED",
        
        # Specific insult combinations
        lambda: f"WEAK ASS JEW",
        lambda: f"WEAK ASS PEDO",
        lambda: f"WEAK ASS SLAVE",
        lambda: f"WEAK ASS WHORE",
        lambda: f"SLOW ASS WHORE",
        lambda: f"DORK ASS SLUT",
        lambda: f"WHORE ASS JR",
        lambda: f"UGLY ASS FEMBOY",
        lambda: f"UGLY ASS QUEER",
        lambda: f"UGLY ASS FECES SNIFFER",
        lambda: f"NASTY ASS JEW",
        lambda: f"JEWISH FAGGOT",
        lambda: f"PINK WEARING FAGGOT",
        lambda: f"BALD NAZI JEW",
        lambda: f"BALD WEAK FUCK",
        lambda: f"STAINED TEETH FUCK",
        lambda: f"BALDY BITCH",
        lambda: f"AUTISTIC FAGGOT",
        lambda: f"WANNABE FAGGOT BOY",
        lambda: f"POORON ASS BOY",
        lambda: f"NIGGAKEST POORON",
        lambda: f"UGLY RUSTED FAGGOT",
        lambda: f"SLUT ASS BOY",
        lambda: f"DOG SHIT QUEER",
        lambda: f"ASS FAT BITCH",
        lambda: f"SLOW FUCKING SPED",
        lambda: f"GARBAGE CAN JEW",
        lambda: f"UGLY FUCKING SHIT BOX",
        lambda: f"SLOW POORON FUCK",
        lambda: f"SLOW LOW TIER FAGGOT",
        lambda: f"LITTLE LGBTQ FUCKTARD",
        lambda: f"STINKY OBESE FAG",
        lambda: f"BORING PEDOPHILE",
        lambda: f"DIRTY FAG",
        
        # Statements with "ur a"
        lambda: f"UR A SLUT {random.choice(DISMISSALS)}",
        lambda: f"UR A SKID",
        lambda: f"UR A FUCKING PUPPET",
        lambda: f"UR A FUCKING NOBODY",
        lambda: f"UR SHITTING URSELF WHORE",
        lambda: f"U HAVE STDS",
        lambda: f"U HAVE A EATING DISORDER",
        lambda: f"U WORSHIP SLAVES",
        lambda: f"U FEAR EVERYONE",
        
        # Shut up variations
        lambda: f"SHUT THE FUCK UP",
        lambda: f"SHUT THE FUCK UP LOSER",
        
        # Why questions
        lambda: f"WHY DO U SMELL LIKE ASS",
        
        # Simple statements
        lambda: f"REJECT",
        lambda: f"BLOWTART",
        lambda: f"DORK FUCK",
        lambda: f"UR UGLY",
        lambda: f"UR SLOW",
        lambda: f"RAPE VICTIM",
    "DAMN",
    "OH",
    ]
    
    # Choose a random pattern and generate a phrase
    pattern_func = random.choice(patterns)
    return pattern_func()

def generate_wordlist(count, output_file, categories=None):
    """Generate a wordlist with the specified number of phrases
    
    Args:
        count: Number of phrases to generate
        output_file: File to save the phrases to
        categories: Dictionary of categories to include (e.g., {"subjects": True, "insults": False})
                   If None, all categories are included
    """
    phrases = set()  # Use a set to avoid duplicates
    
    # Generate phrases until we have the requested count
    while len(phrases) < count:
        phrases.add(generate_phrase())
    
    # Write phrases to the output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# AutoBeef Wordlist - Generated Phrases\n")
        f.write("# Each line is a separate message\n")
        for phrase in phrases:
            f.write(f"{phrase}\n")
    
    print(f"Generated {count} phrases and saved to {output_file}")
    return list(phrases)

def main():
    parser = argparse.ArgumentParser(description="Generate a wordlist of trash talk phrases")
    parser.add_argument("-c", "--count", type=int, default=100, help="Number of phrases to generate")
    parser.add_argument("-o", "--output", default="autobeefwordlist.txt", help="Output file name")
    args = parser.parse_args()
    
    generate_wordlist(args.count, args.output)

if __name__ == "__main__":
    main()#!/usr/bin/env python3
import random
import os
import argparse

"""
Wordlist Generator for AutoBeef

This script generates a wordlist of aggressive/trash-talking phrases
that can be used with the AutoBeef feature in Zephcord.
"""

# Base components for generating phrases
# Start with empty lists that can be populated through the interface
SUBJECTS = []
INSULTS = []
RELATIONS = []
ACTIONS = []
INTENSIFIERS = []
DISMISSALS = []
EXPLETIVES = []

# Add some initial examples to each category
if not SUBJECTS:
    SUBJECTS = ["U", "UR"]

if not INSULTS:
    INSULTS = ["WEAK", "UGLY", "SLOW"]

if not RELATIONS:
    RELATIONS = ["SON", "BITCH", "SLAVE"]

if not ACTIONS:
    ACTIONS = ["SHUT UP", "FOCUS", "KILL URSELF"]

if not INTENSIFIERS:
    INTENSIFIERS = ["FUCKING", "ASS", "VERY"]

if not DISMISSALS:
    DISMISSALS = ["SADLY", "TO", "AND", "AS FUCK"]

if not EXPLETIVES:
    EXPLETIVES = ["FUCK", "FUCKING", "ASS"]

VERBS = ["IS", "ARE", "BE", "COST", "HAVE", "FEAR", "WORSHIP", "SMELL", "LOOK", "SEEM"]

TEMPLATES = [
    # Simple insults
    "{adjective} {adjective} {expletive}",
    "{adjective} {expletive}",
    "{adjective} {adjective}",
    "{adjective} {intensifier}",
    "{adjective} {adjective} {adjective}",
    
    # Subject-based insults
    "{subject} {verb} {adjective}",
    "{subject} {verb} {adjective} {dismissal}",
    "{subject} {verb} {adjective} {intensifier}",
    "{subject} {verb} {expletive} {adjective}",
    "{subject} {verb} {adjective} {adjective}",
    "{subject} {verb} {intensifier} {adjective}",
    
    # Ownership statements
    "UR MY {relation}",
    "UR MY {expletive} {relation}",
    "UR MY {relation} {dismissal}",
    
    # Action templates
    "{action}",
    "{action} {dismissal}",
    
    # Shut up variations
    "SHUT THE {expletive} UP",
    "SHUT THE {expletive} UP {subject}",
    
    # Statements with dismissals
    "{dismissal} {subject} {verb} {adjective}",
    "{dismissal} {subject} {verb} {adjective} {intensifier}",
    "{dismissal} I DONT ADMIRE U",
    "{dismissal} UR {adjective}",
    
    # Statements with "to"
    "UR {dismissal} {adjective}",
    "UR {dismissal} {adjective} {dismissal}",
    "UR {dismissal} {adjective} {intensifier}",
    
    # Statements with "and"
    "UR {adjective} {dismissal} UR {adjective}",
    "{subject} {verb} {adjective} {dismissal} {adjective}",
    
    # Statements with "as fuck"
    "{subject} {verb} {adjective} {dismissal}",
    "{subject} {verb} {intensifier} {dismissal}",
    "{subject} COST 1 {dismissal}",
    
    # Death/failure statements
    "{subject} DIED",
    "{subject} FAILED TO SUICIDE",
    "{subject} STABBED HIMSELF",
    "{subject} SHITS ON HIMSELF",
    "{subject} WORSHIPS GAY FAGGOTS",
    "{subject} WORSHIPS DOG FECES",
    "{subject} STALKS KIDS",
    "{subject} LEECHS OFF ME",
    "{subject} CANT FOCUS",
    
    # Statements with "why"
    "WHY DO U SMELL LIKE {adjective}",
    
    # Statements with "nobody"
    "NOBODY ADMIRES U {dismissal}",
]

def generate_phrase():
    """Generate a random trash talk phrase"""
    # Sometimes generate a completely custom aggressive phrase
    if random.random() < 0.2:  # 20% chance for custom aggressive phrase
        return generate_aggressive_phrase()
    
    template = random.choice(TEMPLATES)
    
    # Replace placeholders with random choices from the corresponding lists
    phrase = template
    if "{subject}" in phrase:
        phrase = phrase.replace("{subject}", random.choice(SUBJECTS))
    if "{verb}" in phrase:
        phrase = phrase.replace("{verb}", random.choice(VERBS))
    if "{adjective}" in phrase:
        phrase = phrase.replace("{adjective}", random.choice(INSULTS))
    if "{relation}" in phrase:
        phrase = phrase.replace("{relation}", random.choice(RELATIONS))
    if "{action}" in phrase:
        phrase = phrase.replace("{action}", random.choice(ACTIONS))
    if "{intensifier}" in phrase:
        phrase = phrase.replace("{intensifier}", random.choice(INTENSIFIERS))
    if "{dismissal}" in phrase:
        phrase = phrase.replace("{dismissal}", random.choice(DISMISSALS))
    if "{expletive}" in phrase:
        phrase = phrase.replace("{expletive}", random.choice(EXPLETIVES))
    
    return phrase

def generate_aggressive_phrase():
    """Generate a more complex aggressive phrase similar to the examples"""
    # Define patterns based on the examples
    patterns = [
        # Simple insults
        lambda: f"{random.choice(INSULTS)} {random.choice(INSULTS)} {random.choice(EXPLETIVES)}",
        lambda: f"{random.choice(INSULTS)} {random.choice(EXPLETIVES)}",
        lambda: f"{random.choice(EXPLETIVES)} {random.choice(INSULTS)}",
        lambda: f"SHIT BAG",
        lambda: f"{random.choice(INSULTS)} {random.choice(INSULTS)} {random.choice(INSULTS)}",
        
        # Action statements
        lambda: f"I DROWNED U IN WATER",
        lambda: f"I HUNT U DAILY",
        lambda: f"IM A THREAT AROUND U",
        lambda: f"ILL TEAR UR SKIN OPEN",
        lambda: f"KILL URSELF",
        lambda: f"RETHINK UR LIFE",
        lambda: f"BEG FOR MERCY",
        lambda: f"KEEP UR EYES OPEN",
        lambda: f"PISS URSELF",
        lambda: f"FOCUS ON ME",
        lambda: f"FOCUS",
        
        # Subject-based insults
        lambda: f"{random.choice(SUBJECTS)} {random.choice(VERBS)} {random.choice(INSULTS)}",
        lambda: f"{random.choice(SUBJECTS)} {random.choice(VERBS)} {random.choice(INSULTS)} {random.choice(DISMISSALS)}",
        lambda: f"{random.choice(DISMISSALS)} {random.choice(SUBJECTS)} {random.choice(VERBS)} {random.choice(INSULTS)}",
        lambda: f"{random.choice(SUBJECTS)} {random.choice(VERBS)} {random.choice(INSULTS)} {random.choice(INTENSIFIERS)}",
        lambda: f"{random.choice(SUBJECTS)} {random.choice(VERBS)} {random.choice(EXPLETIVES)} {random.choice(INSULTS)}",
        
        # Ownership statements
        lambda: f"UR MY {random.choice(RELATIONS)}",
        lambda: f"UR MY {random.choice(EXPLETIVES)} {random.choice(RELATIONS)}",
        lambda: f"UR MY BTICH",
        lambda: f"BOY IS MY BITCH",
        
        # Statements with "to"
        lambda: f"UR TO {random.choice(INSULTS)} {random.choice(DISMISSALS)}",
        lambda: f"UR TO {random.choice(INSULTS)}",
        lambda: f"UR TO {random.choice(INSULTS)} AND A {random.choice(INSULTS)}",
        lambda: f"UR TO BALD SON",
        
        # Statements with "sadly"
        lambda: f"{random.choice(DISMISSALS)} I DONT ADMIRE U",
        lambda: f"{random.choice(DISMISSALS)} UR {random.choice(INSULTS)}",
        lambda: f"{random.choice(DISMISSALS)} UR A {random.choice(INSULTS)}",
        lambda: f"{random.choice(DISMISSALS)} IM A GOD",
        lambda: f"NOBODY ADMIRES U {random.choice(DISMISSALS)}",
        
        # Statements with "and"
        lambda: f"UR {random.choice(INSULTS)} AND UR {random.choice(INSULTS)}",
        lambda: f"UR {random.choice(INSULTS)} AND UR SHIT TO ME",
        lambda: f"UR {random.choice(EXPLETIVES)} {random.choice(INSULTS)} AND UR {random.choice(INSULTS)}",
        
        # Statements with "as fuck"
        lambda: f"{random.choice(SUBJECTS)} {random.choice(VERBS)} {random.choice(INSULTS)} AS FUCK",
        lambda: f"{random.choice(SUBJECTS)} {random.choice(VERBS)} SPED AS FUCK",
        lambda: f"{random.choice(SUBJECTS)} {random.choice(VERBS)} DIRTY AS FUCK",
        lambda: f"{random.choice(SUBJECTS)} COST 1 AND IS SLOW AS FUCK",
        lambda: f"UR POOR AS FUCK",
        lambda: f"JEW IS HORRID AS FUCK",
        
        # Death/failure statements
        lambda: f"{random.choice(SUBJECTS)} DIED",
        lambda: f"{random.choice(SUBJECTS)} FAILED TO SUICIDE",
        lambda: f"{random.choice(SUBJECTS)} STABBED HIMSELF",
        lambda: f"{random.choice(SUBJECTS)} SHITS ON HIMSELF",
        lambda: f"{random.choice(SUBJECTS)} WORSHIPS GAY FAGGOTS",
        lambda: f"{random.choice(SUBJECTS)} WORSHIPS DOG FECES",
        lambda: f"{random.choice(SUBJECTS)} STALKS KIDS",
        lambda: f"{random.choice(SUBJECTS)} LEECHS OFF ME",
        lambda: f"{random.choice(SUBJECTS)} CANT FOCUS",
        lambda: f"SAD REJECT DIED",
        lambda: f"STUPID FAGGOT DIED",
        
        # Specific insult combinations
        lambda: f"WEAK ASS JEW",
        lambda: f"WEAK ASS PEDO",
        lambda: f"WEAK ASS SLAVE",
        lambda: f"WEAK ASS WHORE",
        lambda: f"SLOW ASS WHORE",
        lambda: f"DORK ASS SLUT",
        lambda: f"WHORE ASS JR",
        lambda: f"UGLY ASS FEMBOY",
        lambda: f"UGLY ASS QUEER",
        lambda: f"UGLY ASS FECES SNIFFER",
        lambda: f"NASTY ASS JEW",
        lambda: f"JEWISH FAGGOT",
        lambda: f"PINK WEARING FAGGOT",
        lambda: f"BALD NAZI JEW",
        lambda: f"BALD WEAK FUCK",
        lambda: f"STAINED TEETH FUCK",
        lambda: f"BALDY BITCH",
        lambda: f"AUTISTIC FAGGOT",
        lambda: f"WANNABE FAGGOT BOY",
        lambda: f"POORON ASS BOY",
        lambda: f"NIGGAKEST POORON",
        lambda: f"UGLY RUSTED FAGGOT",
        lambda: f"SLUT ASS BOY",
        lambda: f"DOG SHIT QUEER",
        lambda: f"ASS FAT BITCH",
        lambda: f"SLOW FUCKING SPED",
        lambda: f"GARBAGE CAN JEW",
        lambda: f"UGLY FUCKING SHIT BOX",
        lambda: f"SLOW POORON FUCK",
        lambda: f"SLOW LOW TIER FAGGOT",
        lambda: f"LITTLE LGBTQ FUCKTARD",
        lambda: f"STINKY OBESE FAG",
        lambda: f"BORING PEDOPHILE",
        lambda: f"DIRTY FAG",
        
        # Statements with "ur a"
        lambda: f"UR A SLUT {random.choice(DISMISSALS)}",
        lambda: f"UR A SKID",
        lambda: f"UR A FUCKING PUPPET",
        lambda: f"UR A FUCKING NOBODY",
        lambda: f"UR SHITTING URSELF WHORE",
        lambda: f"U HAVE STDS",
        lambda: f"U HAVE A EATING DISORDER",
        lambda: f"U WORSHIP SLAVES",
        lambda: f"U FEAR EVERYONE",
        
        # Shut up variations
        lambda: f"SHUT THE FUCK UP",
        lambda: f"SHUT THE FUCK UP LOSER",
        
        # Why questions
        lambda: f"WHY DO U SMELL LIKE ASS",
        
        # Simple statements
        lambda: f"REJECT",
        lambda: f"BLOWTART",
        lambda: f"DORK FUCK",
        lambda: f"UR UGLY",
        lambda: f"UR SLOW",
        lambda: f"RAPE VICTIM",
    ]
    
    # Choose a random pattern and generate a phrase
    pattern_func = random.choice(patterns)
    return pattern_func()

def generate_wordlist(count, output_file, categories=None):
    """Generate a wordlist with the specified number of phrases
    
    Args:
        count: Number of phrases to generate
        output_file: File to save the phrases to
        categories: Dictionary of categories to include (e.g., {"subjects": True, "insults": False})
                   If None, all categories are included
    """
    phrases = set()  # Use a set to avoid duplicates
    
    # Generate phrases until we have the requested count
    while len(phrases) < count:
        phrases.add(generate_phrase())
    
    # Write phrases to the output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# AutoBeef Wordlist - Generated Phrases\n")
        f.write("# Each line is a separate message\n")
        for phrase in phrases:
            f.write(f"{phrase}\n")
    
    print(f"Generated {count} phrases and saved to {output_file}")
    return list(phrases)

def main():
    parser = argparse.ArgumentParser(description="Generate a wordlist of trash talk phrases")
    parser.add_argument("-c", "--count", type=int, default=100, help="Number of phrases to generate")
    parser.add_argument("-o", "--output", default="autobeefwordlist.txt", help="Output file name")
    args = parser.parse_args()
    
    generate_wordlist(args.count, args.output)

if __name__ == "__main__":
    main()
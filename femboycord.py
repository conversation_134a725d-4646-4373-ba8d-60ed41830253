import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import requests
import json
import time
import base64
from queue import Queue, Empty 
from threading import Thread, Event, Lock
import os
import re
import random

class DiscordMessageSender:
    def __init__(self, root):
        self.root = root
        self.root.title("Zephcord")
        self.root.geometry("900x650")
        self.root.configure(bg="#0F0B16")
        
        # Print a message to confirm initialization
        print("Starting...")
        
        # Print a message to confirm initialization
        print("Loading...")
        
        self.style = ttk.Style()
        self.style.theme_use('clam')
        self.configure_styles()
        
        self.message_queue = Queue()
        self.rate_limited = False
        self.rate_limit_reset = 0
        self.prefix = ""
        self.suffix = ""
        self.smart_bold_threshold = 0 
        self.space_split_enabled = False
        self.gc_name_changer_mode = False
        self.queue_paused = False
        
        self.base_send_delay = tk.DoubleVar(value=1.5) 
        self.burst_count = 0 
        self.burst_threshold = 5 
        self.burst_delay = 0.000 
        
        # Manual burst mode variables
        self.manual_burst_enabled = False
        self.manual_burst_interval = 20.0  # Seconds between bursts
        self.manual_burst_duration = 5.0   # Seconds to burst for
        self.manual_burst_delay = 1.0      # Default delay (1 sec)
        self.last_burst_time = 0           # Track when the last burst occurred
        self.in_burst_mode = False         # Whether we're currently in a burst

        self.bearer_added = False
        
        self.sending_animation = False
        self.animation_frames = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]
        self.animation_index = 0
        self.button_animations = {}

        self.typing_active = False
        self.typing_timer = None 
        self.typing_thread_stop_event = Event() 
        self.typing_thread = None
        self.last_key_press_time = 0

        self.corrections_file = "corrections.txt"
        self.typo_corrections = {}

        self.transparency_value = tk.DoubleVar(value=100.0)

        self._is_sending_message = False 

        # Add new Mandem mode variables
        self.mandem_mode_enabled = False
        self.mandem_channel_id = ""
        self.mandem_target_id = ""
        self.mandem_target_everyone = False
        self.mandem_wordlist = []
        self.mandem_wordlist_file = "autobeefwordlist.txt"
        self.mandem_reply_chance = 0  # 45% chance to reply
        self.mandem_ping_chance = 0   # 10% chance to ping
        self.mandem_ping_position = "start"  # Where to place the ping: start, end, or after
        self.mandem_ping_after_chance = 0   # 50% chance to ping after message vs. before
        self.mandem_last_message_id = None
        self.mandem_thread = None
        self.mandem_thread_stop_event = Event()
        self.mandem_message_count = 0
        self.mandem_reset_threshold = 30  # Reset after 30 messages
        self.mandem_last_pinged_id = None  # Track the last user we pinged

        # Track used messages to avoid repetition
        self.mandem_used_messages = []
        self.mandem_repeat_threshold = 0.9  # 90% of messages must be used before repeating
        
        # Message collector variables
        self.message_collector_active = False
        self.message_collector_channel_id = ""
        self.message_collector_thread = None
        self.message_collector_thread_stop_event = Event()
        self.message_collector_comma_separated = False
        self.message_collector_last_message_id = None
        
        # Multitoken mode variables
        self.multitoken_active = False
        self.multitoken_tokens = []
        self.multitoken_current_token_index = 0
        self.multitoken_channel_id = ""
        self.multitoken_msgs_until_rotation = 5  # Default value
        self.multitoken_delay = 0  # Default delay in milliseconds (0 = no delay)
        self.multitoken_simultaneous_count = 3  # Default number of simultaneous messages
        self.multitoken_simultaneous_enabled = False  # Disabled by default
        self.multitoken_thread = None
        self.multitoken_thread_stop_event = Event()
        self.multitoken_used_messages = []
        self.multitoken_repeat_threshold = 0.9  # 90% of messages must be used before repeating
        self.multitoken_wordlist = []
        self.multitoken_wordlist_file = "tokenwordlist.txt"
        self.multitoken_message_count = 0
        self.multitoken_rate_limited_tokens = {}  # Changed to dict to store timestamp with token
        self.multitoken_rate_limit_reset_time = 60  # Reset rate limits after 60 seconds
        self.multitoken_spontaneous_chance = 50  # 50% chance for spontaneous messages

        self.setup_ui()
        self.setup_menubar()
        self.setup_hotkeys()
        self.running = True
        self.start_message_sender() 
        self.start_animation() 
        self.load_corrections()
        self.update_menu_states() 

    def configure_styles(self):
        bg_color = "#0F0B16"
        fg_color = "#E2DAEB"
        accent_color = "#7B2CBF"
        secondary_color = "#2A0C4E"
        highlight_color = "#9D4EDD"
        error_color = "#FF3860"
        success_color = "#2ECC71"
        
        self.bg_color = bg_color
        self.fg_color = fg_color
        self.accent_color = accent_color
        self.secondary_color = secondary_color
        self.highlight_color = highlight_color
        self.error_color = error_color
        self.success_color = success_color
        
        self.style.configure('.', 
                           background=bg_color, 
                           foreground=fg_color, 
                           font=('Segoe UI', 9),
                           borderwidth=0,
                           focuscolor=accent_color)
        
        self.style.configure('TFrame', background=bg_color)
        self.style.configure('Header.TFrame', background=secondary_color)
        self.style.configure('Controls.TFrame', background="#1A1425")
        
        self.style.configure('TLabel', 
                           background=bg_color, 
                           foreground=fg_color,
                           font=('Segoe UI', 9))
        self.style.configure('Header.TLabel', 
                           background=secondary_color, 
                           foreground="#FFFFFF",
                           font=('Segoe UI', 14, 'bold'))
        self.style.configure('Section.TLabel', 
                           background=bg_color, 
                           foreground=highlight_color,
                           font=('Segoe UI', 10, 'bold'))
        
        self.style.configure('TEntry', 
                           fieldbackground="#1A1425", 
                           foreground=fg_color, 
                           insertcolor=fg_color,
                           bordercolor="#2A0C4E",
                           lightcolor=bg_color,
                           darkcolor=bg_color,
                           padding=3,
                           relief='flat')
        self.style.map('TEntry',
                      fieldbackground=[('focus', '#231A33')],
                      bordercolor=[('focus', accent_color)])
        
        self.style.configure('TButton', 
                           background=secondary_color, 
                           foreground=fg_color,
                           bordercolor=accent_color,
                           lightcolor=secondary_color,
                           darkcolor=secondary_color,
                           font=('Segoe UI', 9, 'bold'),
                           padding=6,
                           relief='flat')
        self.style.map('TButton', 
                      background=[('active', highlight_color), 
                                 ('pressed', '#5A189A'),
                                 ('disabled', '#1A1425')],
                      foreground=[('active', '#FFFFFF'),
                                 ('disabled', '#6D6D6D')],
                      bordercolor=[('active', highlight_color),
                                  ('pressed', '#5A189A')])
        
        self.style.configure('TCheckbutton', 
                           background=bg_color, 
                           foreground=fg_color,
                           indicatorbackground=bg_color,
                           indicatorcolor=accent_color,
                           font=('Segoe UI', 9))
        self.style.map('TCheckbutton',
                      indicatorbackground=[('selected', accent_color)],
                      foreground=[('active', highlight_color)])

        self.style.configure('TScale',
                             background=bg_color,
                             troughcolor=secondary_color,
                             bordercolor=accent_color,
                             sliderrelief='flat',
                             sliderthickness=15,
                             gripcount=0)
        self.style.map('TScale',
                       background=[('active', highlight_color)],
                       troughcolor=[('active', '#5A189A')])
        
        self.style.configure('TLabelframe', 
                           background=bg_color, 
                           foreground=highlight_color,
                           bordercolor="#2A0C4E",
                           padding=8)
        self.style.configure('TLabelframe.Label', 
                           background=bg_color, 
                           foreground=highlight_color,
                           font=('Segoe UI', 10, 'bold'))
        
        self.text_bg = "#1A1425"
        self.text_fg = "#E2DAEB"
        
        self.style.configure('Vertical.TScrollbar', 
                           background=secondary_color,
                           troughcolor=bg_color,
                           bordercolor=bg_color,
                           arrowcolor=fg_color,
                           relief='flat')
        self.style.map('Vertical.TScrollbar',
                      background=[('active', highlight_color)])
        
        self.root.option_add('*tearOff', False)

        # Add reply tracking variables
        self.reply_to_message_id = None
        self.last_sent_message_id = None


    def animate_button(self, button):
        if button in self.button_animations and self.button_animations[button]:
            return
        
        self.button_animations[button] = True
        original_bg = button.cget('background')
        
        # Smoother animation with more steps
        for i in range(10, 0, -1):
            if not self.button_animations.get(button, False):
                break
            color = self.blend_colors(original_bg, self.accent_color, i/10)
            button.config(background=color)
            button.update_idletasks()
            time.sleep(0.02)  # Slightly faster animation
        
        button.config(background=original_bg)
        self.button_animations[button] = False

    def blend_colors(self, color1, color2, ratio):
        r1, g1, b1 = int(color1[1:3], 16), int(color1[3:5], 16), int(color1[5:7], 16)
        r2, g2, b2 = int(color2[1:3], 16), int(color2[3:5], 16), int(color2[5:7], 16)
        r = int(r1 + (r2 - r1) * ratio)
        g = int(g1 + (g2 - g1) * ratio)
        b = int(b1 + (b2 - b1) * ratio)
        return f"#{r:02x}{g:02x}{b:02x}"

    def setup_menubar(self):
        bg_color = self.secondary_color
        fg_color = self.fg_color
        active_bg_color = self.highlight_color
        active_fg_color = "#FFFFFF"

        menubar = tk.Menu(self.root,
                          bg=bg_color,
                          fg=fg_color,
                          activebackground=active_bg_color,
                          activeforeground=active_fg_color,
                          relief="flat",
                          bd=0)  # Remove border
        self.root.config(menu=menubar)

        options_menu = tk.Menu(menubar,
                               bg=bg_color,
                               fg=fg_color,
                               activebackground=active_bg_color,
                               activeforeground=active_fg_color,
                               relief="flat",
                               bd=0)  # Remove border
        menubar.add_cascade(label="Options", menu=options_menu)

        self.prefix_suffix_var = tk.BooleanVar(value=False)
        options_menu.add_checkbutton(label="Toggle Prefix/Suffix", command=self.toggle_prefix_suffix_from_menu,
                                     variable=self.prefix_suffix_var, onvalue=True, offvalue=False)

        self.space_split_var_menu = tk.BooleanVar(value=False)
        options_menu.add_checkbutton(label="Toggle Space Split", command=self.toggle_space_split_from_menu,
                                     variable=self.space_split_var_menu, onvalue=True, offvalue=False)

        self.gc_name_changer_var_menu = tk.BooleanVar(value=False)
        options_menu.add_checkbutton(label="Toggle GC Name Changer Mode", command=self.toggle_gc_name_changer_mode_from_menu,
                                     variable=self.gc_name_changer_var_menu, onvalue=True, offvalue=False)
                                     
        self.manual_burst_var_menu = tk.BooleanVar(value=False)
        options_menu.add_checkbutton(label="Toggle Manual Burst Mode", command=self.toggle_manual_burst_from_menu,
                                     variable=self.manual_burst_var_menu, onvalue=True, offvalue=False)

        options_menu.add_separator()
        
        options_menu.add_command(label="Adjust Transparency", command=self.open_transparency_dialog)

        hotkeys_menu = tk.Menu(menubar,
                               bg=bg_color,
                               fg=fg_color,
                               activebackground=active_bg_color,
                               activeforeground=active_fg_color,
                               relief="flat",
                               bd=0)  # Remove border
        menubar.add_cascade(label="Help", menu=hotkeys_menu)
        hotkeys_menu.add_command(label="Show Hotkeys", command=self.show_hotkeys_dialog)
        
        self.update_menu_states()


    def update_menu_states(self):
        self.prefix_suffix_var.set(bool(self.prefix_entry.get() or self.suffix_entry.get()))
        self.space_split_var_menu.set(self.space_split_enabled)
        self.gc_name_changer_var_menu.set(self.gc_name_changer_mode)
        self.manual_burst_var_menu.set(self.manual_burst_enabled)


    def toggle_prefix_suffix_from_menu(self):
        self.toggle_prefix_suffix()
        self.update_menu_states()

    def toggle_space_split_from_menu(self):
        self.toggle_space_split()
        self.update_menu_states()

    def toggle_gc_name_changer_mode_from_menu(self):
        self.toggle_gc_name_changer_mode()
        self.update_menu_states()
        
    def toggle_manual_burst_from_menu(self):
        self.manual_burst_enabled = not self.manual_burst_enabled
        self.manual_burst_var.set(self.manual_burst_enabled)
        self.toggle_manual_burst_mode()
        
        # If enabling manual burst mode, show a message box to inform the user
        if self.manual_burst_enabled:
            messagebox.showinfo("Manual Burst Mode Enabled", 
                               f"Manual burst mode has been enabled.\n\n"
                               f"• Bursts will occur every {self.manual_burst_interval:.0f}s for {self.manual_burst_duration:.0f}s\n"
                               f"• During bursts, doubled messages will be sent with zero delay\n"
                               f"• Queue processing will be temporarily paused during bursts\n"
                               f"• Other message sending modes have been disabled")
        
        self.update_menu_states()


    def open_transparency_dialog(self):
        dialog = tk.Toplevel(self.root)
        dialog.title("Adjust Transparency")
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.resizable(False, False)
        dialog.configure(bg=self.style.lookup('TFrame', 'background'))

        ttk.Label(dialog, text="Window Opacity:", style='TLabel').pack(pady=10)

        slider = ttk.Scale(dialog,
                           from_=10, to=100,
                           orient=tk.HORIZONTAL,
                           variable=self.transparency_value,
                           command=self.update_transparency,
                           length=250,
                           style='TScale')
        slider.pack(padx=20, pady=5)

        self.transparency_label_dialog = ttk.Label(dialog, text=f"Opacity: {int(self.transparency_value.get())}%", style='TLabel')
        self.transparency_label_dialog.pack(pady=5)
        
        self.transparency_value.trace_add('write', lambda *args: self.transparency_label_dialog.config(text=f"Opacity: {int(self.transparency_value.get())}%"))

        ttk.Button(dialog, text="Close", command=dialog.destroy, style='TButton').pack(pady=10)

        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() // 2) - (dialog.winfo_width() // 2)
        y = self.root.winfo_y() + (self.root.winfo_height() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{int(x)}+{int(y)}")

        self.root.wait_window(dialog)

    def show_hotkeys_dialog(self):
        hotkey_text = """
        Enter: Send Message / Set Group Name
        Shift+Enter: Insert New Line
        F1: Toggle Prefix/Suffix
        F2: Toggle Space Split
        F3: Toggle GC Name Changer Mode
        F4: Pause/Resume Queue
        """
        messagebox.showinfo("Hotkeys", hotkey_text)

    def setup_ui(self):
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create Manual tab (existing UI)
        manual_tab = ttk.Frame(self.notebook)
        self.notebook.add(manual_tab, text="Manual")
        
        # Create AutoBeef tab
        mandem_tab = ttk.Frame(self.notebook)
        self.notebook.add(mandem_tab, text="AutoBeef")
        
        # Create Multitoken tab
        multitoken_tab = ttk.Frame(self.notebook)
        self.notebook.add(multitoken_tab, text="Multitoken")
        
        # Create Options tab
        options_tab = ttk.Frame(self.notebook)
        self.notebook.add(options_tab, text="Options")
        
        # Create Wordlist Generator tab
        wordlist_gen_tab = ttk.Frame(self.notebook)
        self.notebook.add(wordlist_gen_tab, text="Wordlist Gen")
        
        # Setup Manual tab (move existing UI elements here)
        self.setup_manual_tab(manual_tab)
        
        # Setup Mandem tab
        self.setup_mandem_tab(mandem_tab)
        
        # Setup Multitoken tab
        self.setup_multitoken_tab(multitoken_tab)
        
        # Setup Options tab
        self.setup_options_tab(options_tab)
        
        # Setup Wordlist Generator tab
        self.setup_wordlist_gen_tab(wordlist_gen_tab)
        
        # Status bar remains at bottom of main window
        status_frame = ttk.Frame(self.root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_var = tk.StringVar(value="Ready | Queue: 0")
        self.status_bar = ttk.Label(status_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(fill=tk.X)

    def setup_manual_tab(self, tab):
        main_frame = ttk.Frame(tab)
        main_frame.pack(fill=tk.BOTH, expand=True)

        left_pane = ttk.Frame(main_frame)
        left_pane.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        right_pane = ttk.Frame(main_frame, width=280, style='Controls.TFrame')
        right_pane.pack(side=tk.RIGHT, fill=tk.Y, padx=(5,0))
        right_pane.pack_propagate(False)
        
        header = ttk.Frame(left_pane, style='Header.TFrame')
        header.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(header, text="Zephcord", style='Header.TLabel').pack(side=tk.LEFT, padx=10, pady=5)
        
        token_frame = ttk.Frame(left_pane)
        token_frame.pack(fill=tk.X, pady=3)
        ttk.Label(token_frame, text="Discord Token:", style='Section.TLabel').pack(anchor=tk.W)
        
        self.token_var = tk.StringVar()
        self.token_var.trace_add('write', self.handle_token_input)
        self.token_entry = ttk.Entry(token_frame, textvariable=self.token_var, style='TEntry')
        self.token_entry.pack(fill=tk.X, pady=3)
        
        channel_frame = ttk.Frame(left_pane)
        channel_frame.pack(fill=tk.X, pady=3)
        ttk.Label(channel_frame, text="Channel ID:", style='Section.TLabel').pack(anchor=tk.W)
        self.channel_entry = ttk.Entry(channel_frame, style='TEntry')
        self.channel_entry.pack(fill=tk.X, pady=3)
        
        msg_frame = ttk.Frame(left_pane)
        msg_frame.pack(fill=tk.BOTH, expand=True, pady=3) 
        
        self.message_input_label = ttk.Label(msg_frame, text="Message:", style='Section.TLabel')
        self.message_input_label.pack(anchor=tk.NW)
        
        text_frame = ttk.Frame(msg_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.message_entry = tk.Text(text_frame, 
                                   height=7, 
                                   wrap=tk.WORD, 
                                   bg=self.text_bg, 
                                   fg=self.text_fg,
                                   insertbackground="#FFFFFF", 
                                   selectbackground=self.accent_color,
                                   font=('Segoe UI', 10),
                                   padx=8,
                                   pady=8,
                                   relief='flat',
                                   highlightthickness=1,
                                   highlightcolor=self.accent_color,
                                   highlightbackground="#505050")
        self.message_entry.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.log_text = None
        self.status_var = None
        self.status_bar = None
        self.typo_corrections = {}
        self.corrections_file = "typo_corrections.txt"
        self.base_send_delay = tk.DoubleVar(value=0.5)
        self.transparency_value = tk.DoubleVar(value=100.0)
        self.animation_index = 0
        self.animation_frames = ["Sending... | Queue: ", "Sending! | Queue: ", "Sending! | Queue! ", "Sending! | Queue! "]
        self._is_sending_message = False
        self.message_queue = Queue()
        self.setup_styles()
        self.configure_styles()
        # Removed recursive call to setup_ui()
        self.setup_hotkeys()
        self.load_corrections()
        self.start_message_sender()
        self.start_animation()

    def setup_styles(self):
        self.style = ttk.Style(self.root)
        self.style.theme_use('clam')

    def configure_styles(self):
        # Modern gray color scheme
        bg_color = "#2C2C2C"
        fg_color = "#E8E8E8"
        accent_color = "#4285F4"  # Google blue
        secondary_color = "#3C3C3C"
        highlight_color = "#5C9CE6"
        error_color = "#EA4335"  # Google red
        success_color = "#34A853"  # Google green
        
        self.bg_color = bg_color
        self.fg_color = fg_color
        self.accent_color = accent_color
        self.secondary_color = secondary_color
        self.highlight_color = highlight_color
        self.error_color = error_color
        self.success_color = success_color
        
        self.style.configure('.', 
                           background=bg_color, 
                           foreground=fg_color, 
                           font=('Segoe UI', 9),
                           borderwidth=0,
                           focuscolor=accent_color)
        
        self.style.configure('TFrame', background=bg_color)
        self.style.configure('Header.TFrame', background=secondary_color)
        self.style.configure('Controls.TFrame', background="#383838")
        
        self.style.configure('TLabel', 
                           background=bg_color, 
                           foreground=fg_color,
                           font=('Segoe UI', 9))
        self.style.configure('Header.TLabel', 
                           background=secondary_color, 
                           foreground="#FFFFFF",
                           font=('Segoe UI', 14, 'bold'))
        self.style.configure('Section.TLabel', 
                           background=bg_color, 
                           foreground=highlight_color,
                           font=('Segoe UI', 10, 'bold'))
        
        self.style.configure('TEntry', 
                           fieldbackground="#383838", 
                           foreground=fg_color, 
                           insertcolor=fg_color,
                           bordercolor="#505050",
                           lightcolor=bg_color,
                           darkcolor=bg_color,
                           padding=3,
                           relief='flat')
        self.style.map('TEntry',
                      fieldbackground=[('focus', '#454545')],
                      bordercolor=[('focus', accent_color)])
        
        self.style.configure('TButton', 
                           background=secondary_color, 
                           foreground=fg_color,
                           bordercolor=accent_color,
                           lightcolor=secondary_color,
                           darkcolor=secondary_color,
                           font=('Segoe UI', 9, 'bold'),
                           padding=6,
                           relief='flat')
        self.style.map('TButton', 
                      background=[('active', highlight_color), 
                                 ('pressed', '#3C78D8'),
                                 ('disabled', '#383838')],
                      foreground=[('active', '#FFFFFF'),
                                 ('disabled', '#6D6D6D')],
                      bordercolor=[('active', highlight_color),
                                  ('pressed', '#3C78D8')])
        
        self.style.configure('TCheckbutton', 
                           background=bg_color, 
                           foreground=fg_color,
                           indicatorbackground=bg_color,
                           indicatorcolor=accent_color,
                           font=('Segoe UI', 9))
        self.style.map('TCheckbutton',
                      indicatorbackground=[('selected', accent_color)],
                      foreground=[('active', highlight_color)])

        self.style.configure('TScale',
                             background=bg_color,
                             troughcolor=secondary_color,
                             bordercolor=accent_color,
                             sliderrelief='flat',
                             sliderthickness=15,
                             gripcount=0)
        self.style.map('TScale',
                       background=[('active', highlight_color)],
                       troughcolor=[('active', '#3C78D8')])
        
        self.style.configure('TLabelframe', 
                           background=bg_color, 
                           foreground=highlight_color,
                           bordercolor="#505050",
                           padding=8)
        self.style.configure('TLabelframe.Label', 
                           background=bg_color, 
                           foreground=highlight_color,
                           font=('Segoe UI', 10, 'bold'))
        
        self.text_bg = "#383838"
        self.text_fg = "#E8E8E8"
        
        self.style.configure('Vertical.TScrollbar', 
                           background=secondary_color,
                           troughcolor=bg_color,
                           bordercolor=bg_color,
                           arrowcolor=fg_color,
                           relief='flat')
        self.style.map('Vertical.TScrollbar',
                      background=[('active', highlight_color)])
        
        self.root.option_add('*tearOff', False)

    def setup_manual_tab(self, parent):
        main_frame = ttk.Frame(parent)
        main_frame.pack(fill=tk.BOTH, expand=True)

        left_pane = ttk.Frame(main_frame)
        left_pane.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        right_pane = ttk.Frame(main_frame, width=280, style='Controls.TFrame')
        right_pane.pack(side=tk.RIGHT, fill=tk.Y, padx=(5,0))
        right_pane.pack_propagate(False)
        
        header = ttk.Frame(left_pane, style='Header.TFrame')
        header.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(header, text="Zephcord", style='Header.TLabel').pack(side=tk.LEFT, padx=10, pady=5)
        
        token_frame = ttk.Frame(left_pane)
        token_frame.pack(fill=tk.X, pady=3)
        ttk.Label(token_frame, text="Discord Token:", style='Section.TLabel').pack(anchor=tk.W)
        
        self.token_var = tk.StringVar()
        self.token_var.trace_add('write', self.handle_token_input)
        self.token_entry = ttk.Entry(token_frame, textvariable=self.token_var, style='TEntry')
        self.token_entry.pack(fill=tk.X, pady=3)
        
        channel_frame = ttk.Frame(left_pane)
        channel_frame.pack(fill=tk.X, pady=3)
        ttk.Label(channel_frame, text="Channel ID:", style='Section.TLabel').pack(anchor=tk.W)
        self.channel_entry = ttk.Entry(channel_frame, style='TEntry')
        self.channel_entry.pack(fill=tk.X, pady=3)
        
        msg_frame = ttk.Frame(left_pane)
        msg_frame.pack(fill=tk.BOTH, expand=True, pady=3) 
        
        self.message_input_label = ttk.Label(msg_frame, text="Message:", style='Section.TLabel')
        self.message_input_label.pack(anchor=tk.NW)
        
        text_frame = ttk.Frame(msg_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.message_entry = tk.Text(text_frame, 
                                   height=7, 
                                   wrap=tk.WORD, 
                                   bg=self.text_bg, 
                                   fg=self.text_fg,
                                   insertbackground="#FFFFFF", 
                                   selectbackground=self.accent_color,
                                   font=('Segoe UI', 10),
                                   padx=8,
                                   pady=8,
                                   relief='flat',
                                   highlightthickness=1,
                                   highlightcolor=self.accent_color,
                                   highlightbackground="#505050")
        self.message_entry.pack(side=tk.LEFT, fill=tk.BOTH, expand=True) 
        
        scrollbar = ttk.Scrollbar(text_frame, command=self.message_entry.yview, style='Vertical.TScrollbar')
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.message_entry.config(yscrollcommand=scrollbar.set)
        
        self.message_entry.bind("<Key>", self.on_key_press_for_typing)
        self.message_entry.bind("<FocusOut>", self.stop_typing_indicator)
        
        btn_frame = ttk.Frame(left_pane)
        btn_frame.pack(fill=tk.X, pady=8)
        
        send_btn = ttk.Button(btn_frame, text="Send/Set", command=lambda: [self.queue_message(), self.animate_button(send_btn)]) 
        send_btn.pack(side=tk.LEFT, padx=3, fill=tk.X, expand=True) 
        
        clear_btn = ttk.Button(btn_frame, text="Clear Queue", command=lambda: [self.clear_queue(), self.animate_button(clear_btn)])
        clear_btn.pack(side=tk.LEFT, padx=3, fill=tk.X, expand=True) 
        
        self.status_var = tk.StringVar()
        self.status_var.set("Ready | Queue: 0")
        self.status_bar = ttk.Label(left_pane, 
                                  textvariable=self.status_var, 
                                  relief=tk.SUNKEN, 
                                  anchor=tk.W,
                                  style='TLabel',
                                  background="#1A1425",
                                  font=('Segoe UI', 8))
        self.status_bar.pack(fill=tk.X, pady=(8, 0))
        
        log_frame = ttk.Frame(left_pane)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=3) 
        ttk.Label(log_frame, text="Activity Log:", style='Section.TLabel').pack(anchor=tk.W)
        
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = tk.Text(log_text_frame, 
                              height=6, 
                              state=tk.DISABLED, 
                              bg=self.text_bg, 
                              fg=self.text_fg,
                              insertbackground="#FFFFFF", 
                              selectbackground=self.accent_color,
                              font=('Consolas', 8),
                              padx=8,
                              pady=8,
                              relief='flat',
                               highlightthickness=1,
                               highlightcolor=self.accent_color,
                               highlightbackground="#505050")
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True) 
        
        log_scroll = ttk.Scrollbar(log_text_frame, command=self.log_text.yview, style='Vertical.TScrollbar')
        log_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=log_scroll.set)
        
        ttk.Label(right_pane, text="Message Options", style='Header.TLabel').pack(pady=8)
        
        features_frame = ttk.LabelFrame(right_pane, text="Message Features", padding=8)
        features_frame.pack(fill=tk.X, pady=8, padx=5)
        
        prefix_frame = ttk.Frame(features_frame)
        prefix_frame.pack(fill=tk.X, pady=3)
        ttk.Label(prefix_frame, text="Prefix:").pack(anchor=tk.W)
        self.prefix_entry = ttk.Entry(prefix_frame, style='TEntry')
        self.prefix_entry.pack(fill=tk.X, pady=1)
        self.prefix_entry.bind("<KeyRelease>", lambda e: self.update_menu_states())
        
        suffix_frame = ttk.Frame(features_frame)
        suffix_frame.pack(fill=tk.X, pady=3)
        ttk.Label(suffix_frame, text="Suffix:").pack(anchor=tk.W)
        self.suffix_entry = ttk.Entry(suffix_frame, style='TEntry')
        self.suffix_entry.pack(fill=tk.X, pady=1)
        self.suffix_entry.bind("<KeyRelease>", lambda e: self.update_menu_states())
        
        bold_frame = ttk.Frame(features_frame)
        bold_frame.pack(fill=tk.X, pady=3)
        ttk.Label(bold_frame, text="Smart Bold Threshold (0=off):").pack(anchor=tk.W)
        self.bold_threshold_entry = ttk.Entry(bold_frame, style='TEntry', width=5)
        self.bold_threshold_entry.pack(anchor=tk.W, pady=1)
        self.bold_threshold_entry.insert(0, str(self.smart_bold_threshold)) 
        
        self.space_split_var_ui = tk.IntVar()
        self.space_split_btn = ttk.Checkbutton(features_frame, 
                                        text="Split spaces to newlines", 
                                        variable=self.space_split_var_ui,
                                        command=self.toggle_space_split_ui)
        self.space_split_btn.pack(anchor=tk.W, pady=3)

        self.gc_name_changer_var_ui = tk.IntVar()
        self.gc_name_changer_check = ttk.Checkbutton(features_frame,
                                                  text="GC Name Changer Mode",
                                                  variable=self.gc_name_changer_var_ui,
                                                  command=self.toggle_gc_name_changer_mode_ui)
        self.gc_name_changer_check.pack(anchor=tk.W, pady=3)
        
        delay_frame = ttk.LabelFrame(right_pane, text="Send Delay", padding=5)
        delay_frame.pack(fill=tk.X, pady=5, padx=5)

        self.delay_label = ttk.Label(delay_frame, text=f"Delay: {self.base_send_delay.get():.2f}s", style='TLabel')
        self.delay_label.pack(anchor=tk.W, pady=1)
        
        self.delay_slider = ttk.Scale(delay_frame, 
                                     from_=0.1, to=5.0, 
                                     orient=tk.HORIZONTAL, 
                                     variable=self.base_send_delay,
                                     command=self.update_delay_label,
                                     style='TScale')
        self.delay_slider.pack(fill=tk.X, pady=2)
        
        # Create a variable for the manual burst mode checkbox in the manual tab
        self.manual_burst_var = tk.BooleanVar(value=False)
        
        typo_frame = ttk.LabelFrame(right_pane, text="Typo Corrections", padding=8)
        typo_frame.pack(fill=tk.BOTH, expand=True, pady=8, padx=5) 

        ttk.Label(typo_frame, text="Word (typo):").pack(anchor=tk.W)
        self.typo_word_entry = ttk.Entry(typo_frame, style='TEntry')
        self.typo_word_entry.pack(fill=tk.X, pady=1)
        self.typo_word_entry.bind("<Return>", lambda e: self.add_correction_from_entries())

        ttk.Label(typo_frame, text="Correction:").pack(anchor=tk.W)
        self.correction_entry = ttk.Entry(typo_frame, style='TEntry')
        self.correction_entry.pack(fill=tk.X, pady=1)
        self.correction_entry.bind("<Return>", lambda e: self.add_correction_from_entries())

        self.add_correction_btn = ttk.Button(typo_frame, text="Add/Update Correction", command=lambda: [self.add_correction_from_entries(), self.animate_button(self.add_correction_btn)])
        self.add_correction_btn.pack(fill=tk.X, pady=3)
        
        self.load_correction_btn = ttk.Button(typo_frame, text="Reload Corrections from File", command=lambda: [self.load_corrections(), self.animate_button(self.load_correction_btn)])
        self.load_correction_btn.pack(fill=tk.X, pady=3)

        ttk.Label(typo_frame, text="Current Corrections:").pack(anchor=tk.W, pady=(5,1))
        self.current_corrections_text = tk.Text(typo_frame, 
                                            height=4, 
                                            state=tk.DISABLED, 
                                            bg=self.text_bg, 
                                            fg=self.text_fg,
                                            font=('Consolas', 8),
                                            padx=5,
                                            pady=5,
                                            relief='flat',
                                            highlightthickness=1,
                                            highlightcolor="#2A0C4E",
                                            highlightbackground="#2A0C4E")
        self.current_corrections_text.pack(fill=tk.BOTH, expand=True) 

        transparency_frame = ttk.LabelFrame(right_pane, text="Transparency", padding=8)
        transparency_frame.pack(fill=tk.X, pady=8, padx=5)

        self.transparency_label = ttk.Label(transparency_frame, text=f"Opacity: {int(self.transparency_value.get())}%", style='TLabel')
        self.transparency_label.pack(anchor=tk.W, pady=2)

        self.transparency_slider = ttk.Scale(transparency_frame,
                                              from_=10, to=100,
                                              orient=tk.HORIZONTAL,
                                              variable=self.transparency_value,
                                              command=self.update_transparency,
                                              style='TScale')
        self.transparency_slider.pack(fill=tk.X, pady=3)

    def update_delay_label(self, *args):
        self.delay_label.config(text=f"Delay: {self.base_send_delay.get():.2f}s")

    def update_transparency(self, value):
        alpha_val = float(value) / 100.0
        self.root.attributes("-alpha", alpha_val)
        self.transparency_label.config(text=f"Opacity: {int(float(value))}%")
        if hasattr(self, 'transparency_label_dialog') and self.transparency_label_dialog.winfo_exists():
             self.transparency_label_dialog.config(text=f"Opacity: {int(float(value))}%")
             
    def toggle_manual_burst_mode(self):
        self.manual_burst_enabled = self.manual_burst_var.get()
        if self.manual_burst_enabled:
            self.log("Manual burst mode enabled. Messages will burst every " + 
                    f"{self.manual_burst_interval:.0f}s for {self.manual_burst_duration:.0f}s", False)
            self.log("During bursts, doubled messages will be sent with zero delay", False)
            # Reset burst timing
            self.last_burst_time = time.time()
            self.in_burst_mode = False
            
            # Disable other message sending modes when manual burst is enabled
            self.space_split_enabled = False
            self.space_split_var_menu.set(False)
            self.space_split_var_ui.set(False)
            
            # Disable GC name changer mode
            self.gc_name_changer_mode = False
            self.gc_name_changer_var_menu.set(False)
            self.gc_name_changer_var_ui.set(False)
            
            # Reset burst count to prevent stacking with other burst mechanisms
            self.burst_count = 0
            
            # Set burst delay to zero for immediate sending during bursts
            self.burst_delay = 0.0
            
            # Update UI to reflect changes
            self.log("Other message sending modes have been disabled to prevent stacking", False)
        else:
            self.log("Manual burst mode disabled", False)
            self.in_burst_mode = False
            # Ensure queue is not left paused when disabling
            self.queue_paused = False
    
    def update_manual_delay(self, value):
        self.manual_burst_delay = float(value)
        self.manual_delay_label.config(text=f"Normal Delay: {self.manual_burst_delay:.2f}s")
    
    def update_burst_interval(self, value):
        self.manual_burst_interval = float(value)
        self.burst_interval_label.config(text=f"Burst Every: {self.manual_burst_interval:.0f}s")
        # Reset burst timing when interval changes
        if self.manual_burst_enabled:
            self.last_burst_time = time.time()
            self.in_burst_mode = False
    
    def update_burst_duration(self, value):
        self.manual_burst_duration = float(value)
        self.burst_duration_label.config(text=f"Burst Duration: {self.manual_burst_duration:.0f}s")
        
    def setup_wordlist_gen_tab(self, parent):
        """Setup the Wordlist Generator tab"""
        # Create a two-column layout
        paned_window = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        left_pane = ttk.Frame(paned_window)
        right_pane = ttk.Frame(paned_window)
        paned_window.add(left_pane, weight=3)
        paned_window.add(right_pane, weight=2)
        
        # Left pane - Generator settings
        settings_frame = ttk.LabelFrame(left_pane, text="Generator Settings")
        settings_frame.pack(fill=tk.BOTH, expand=True, pady=5, padx=5)
        
        # Number of phrases to generate
        count_frame = ttk.Frame(settings_frame)
        count_frame.pack(fill=tk.X, pady=5)
        ttk.Label(count_frame, text="Number of phrases:").pack(side=tk.LEFT, padx=5)
        self.wordlist_gen_count_var = tk.IntVar(value=100)
        count_spinbox = ttk.Spinbox(
            count_frame, 
            from_=10, 
            to=1000, 
            increment=10,
            textvariable=self.wordlist_gen_count_var,
            width=5
        )
        count_spinbox.pack(side=tk.LEFT, padx=5)
        
        # Output file
        file_frame = ttk.Frame(settings_frame)
        file_frame.pack(fill=tk.X, pady=5)
        ttk.Label(file_frame, text="Output file:").pack(side=tk.LEFT, padx=5)
        self.wordlist_gen_output_var = tk.StringVar(value=self.mandem_wordlist_file)
        file_entry = ttk.Entry(file_frame, textvariable=self.wordlist_gen_output_var)
        file_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        # Category selection
        category_frame = ttk.LabelFrame(settings_frame, text="Include Categories")
        category_frame.pack(fill=tk.X, pady=5, padx=5)
        
        # Create checkboxes for each category
        self.category_vars = {}
        self.categories = [
            ("Subjects", "SUBJECTS", "U, UR, BOY, etc."),
            ("Insults", "INSULTS", "WEAK, UGLY, SLOW, etc."),
            ("Relations", "RELATIONS", "SON, BITCH, SLAVE, etc."),
            ("Actions", "ACTIONS", "SHUT UP, FOCUS, KILL URSELF, etc."),
            ("Intensifiers", "INTENSIFIERS", "FUCKING, ASS, VERY, etc."),
            ("Dismissals", "DISMISSALS", "SADLY, TO, AND, AS FUCK, etc."),
            ("Expletives", "EXPLETIVES", "FUCK, FUCKING, ASS, etc.")
        ]
        
        for i, (name, var_name, desc) in enumerate(self.categories):
            var = tk.BooleanVar(value=True)
            self.category_vars[var_name] = var
            cb = ttk.Checkbutton(
                category_frame,
                text=f"{name} ({desc})",
                variable=var
            )
            cb.pack(anchor=tk.W, pady=2, padx=5)
        
        # Generate button
        generate_btn = ttk.Button(
            settings_frame,
            text="Generate Wordlist",
            command=self.generate_wordlist
        )
        generate_btn.pack(fill=tk.X, pady=10, padx=5)
        
        # Test button - Generate and show sample phrases
        test_btn = ttk.Button(
            settings_frame,
            text="Generate Sample Phrases",
            command=self.generate_sample_phrases
        )
        test_btn.pack(fill=tk.X, pady=5, padx=5)
        
        # Right pane - Custom word addition
        custom_frame = ttk.LabelFrame(right_pane, text="Add Custom Words")
        custom_frame.pack(fill=tk.BOTH, expand=True, pady=5, padx=5)
        
        # Category selection for custom words
        ttk.Label(custom_frame, text="Select category:").pack(anchor=tk.W, pady=2, padx=5)
        self.custom_category_var = tk.StringVar(value="SUBJECTS")
        category_combo = ttk.Combobox(
            custom_frame,
            textvariable=self.custom_category_var,
            values=[cat[1] for cat in self.categories],
            state="readonly"
        )
        category_combo.pack(fill=tk.X, pady=2, padx=5)
        
        # Custom word entry
        ttk.Label(custom_frame, text="Enter custom word:").pack(anchor=tk.W, pady=2, padx=5)
        self.custom_word_var = tk.StringVar()
        custom_entry = ttk.Entry(custom_frame, textvariable=self.custom_word_var)
        custom_entry.pack(fill=tk.X, pady=2, padx=5)
        custom_entry.bind("<Return>", lambda e: self.add_custom_word())
        
        # Add button
        add_btn = ttk.Button(
            custom_frame,
            text="Add Word",
            command=self.add_custom_word
        )
        add_btn.pack(fill=tk.X, pady=5, padx=5)
        
        # Bulk add section
        ttk.Label(custom_frame, text="Bulk add words (one per line):").pack(anchor=tk.W, pady=2, padx=5)
        
        # Create a text widget for bulk adding words
        self.bulk_words_entry = tk.Text(custom_frame, height=5, wrap=tk.WORD)
        self.bulk_words_entry.pack(fill=tk.X, expand=False, pady=2, padx=5)
        
        # Bulk add button
        bulk_add_btn = ttk.Button(
            custom_frame,
            text="Add All Words",
            command=self.bulk_add_words
        )
        bulk_add_btn.pack(fill=tk.X, pady=5, padx=5)
        
        # Current words in category
        ttk.Label(custom_frame, text="Current words in category:").pack(anchor=tk.W, pady=2, padx=5)
        
        # Create a text widget to display current words
        self.category_words_display = tk.Text(custom_frame, height=10, wrap=tk.WORD)
        self.category_words_display.pack(fill=tk.BOTH, expand=True, pady=2, padx=5)
        
        # Add a scrollbar
        scrollbar = ttk.Scrollbar(self.category_words_display, command=self.category_words_display.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.category_words_display.config(yscrollcommand=scrollbar.set)
        
        # Delete word button
        delete_word_btn = ttk.Button(
            custom_frame,
            text="Delete Selected Word",
            command=self.delete_selected_word
        )
        delete_word_btn.pack(fill=tk.X, pady=5, padx=5)
        
        # Clear category button
        clear_category_btn = ttk.Button(
            custom_frame,
            text="Clear Entire Category",
            command=self.clear_category
        )
        clear_category_btn.pack(fill=tk.X, pady=5, padx=5)
        
        # Update the display when category changes
        category_combo.bind("<<ComboboxSelected>>", lambda e: self.update_category_words_display())
        
        # Initial update of the display
        self.update_category_words_display()
        
    def update_category_words_display(self):
        """Update the display of words in the selected category"""
        category = self.custom_category_var.get()
        
        # Get the words from wordlist_gen.py
        try:
            # Import the module
            import wordlist_gen
            import importlib
            importlib.reload(wordlist_gen)  # Reload to get any changes
            
            # Get the list of words for the selected category
            words = getattr(wordlist_gen, category, [])
            
            # Update the display
            self.category_words_display.config(state=tk.NORMAL)
            self.category_words_display.delete("1.0", tk.END)
            for i, word in enumerate(words):
                self.category_words_display.insert(tk.END, f"{i+1}. {word}\n")
            self.category_words_display.config(state=tk.NORMAL)
        except Exception as e:
            self.log(f"✗ Error loading words from wordlist_gen.py: {str(e)}", True)
            self.category_words_display.config(state=tk.NORMAL)
            self.category_words_display.delete("1.0", tk.END)
            self.category_words_display.insert(tk.END, f"Error: {str(e)}")
            self.category_words_display.config(state=tk.NORMAL)
    
    def add_custom_word(self):
        """Add a custom word to the selected category in wordlist_gen.py"""
        category = self.custom_category_var.get()
        word = self.custom_word_var.get().strip().upper()  # Convert to uppercase
        
        if not word:
            self.log("✗ Error: Please enter a word to add.", True)
            return
        
        try:
            # Read the current file
            with open("c:/Users/<USER>/Downloads/femboycord/wordlist_gen.py", "r", encoding="utf-8") as f:
                lines = f.readlines()
            
            # Find the category in the file
            category_start = -1
            category_end = -1
            for i, line in enumerate(lines):
                if line.strip().startswith(f"{category} = ["):
                    category_start = i
                elif category_start != -1 and line.strip().startswith("]"):
                    category_end = i
                    break
            
            if category_start == -1 or category_end == -1:
                self.log(f"✗ Error: Could not find {category} in wordlist_gen.py", True)
                return
            
            # Check if the word already exists
            for i in range(category_start + 1, category_end):
                if f'"{word}"' in lines[i]:
                    self.log(f"✗ Word '{word}' already exists in {category}", True)
                    return
            
            # Add the word before the closing bracket
            lines.insert(category_end, f'    "{word}",\n')
            
            # Write the file back
            with open("c:/Users/<USER>/Downloads/femboycord/wordlist_gen.py", "w", encoding="utf-8") as f:
                f.writelines(lines)
            
            self.log(f"✓ Added '{word}' to {category}")
            
            # Clear the entry
            self.custom_word_var.set("")
            
            # Update the display
            self.update_category_words_display()
            
        except Exception as e:
            self.log(f"✗ Error adding word to wordlist_gen.py: {str(e)}", True)
    
    def bulk_add_words(self):
        """Add multiple words at once to the selected category"""
        category = self.custom_category_var.get()
        text = self.bulk_words_entry.get("1.0", tk.END).strip()
        
        if not text:
            self.log("✗ Error: Please enter words to add.", True)
            return
        
        # Split the text into words
        words = [word.strip().upper() for word in text.split("\n") if word.strip()]
        
        if not words:
            self.log("✗ Error: No valid words found.", True)
            return
        
        try:
            # Read the current file
            with open("c:/Users/<USER>/Downloads/femboycord/wordlist_gen.py", "r", encoding="utf-8") as f:
                lines = f.readlines()
            
            # Find the category in the file
            category_start = -1
            category_end = -1
            for i, line in enumerate(lines):
                if line.strip().startswith(f"{category} = ["):
                    category_start = i
                elif category_start != -1 and line.strip().startswith("]"):
                    category_end = i
                    break
            
            if category_start == -1 or category_end == -1:
                self.log(f"✗ Error: Could not find {category} in wordlist_gen.py", True)
                return
            
            # Get existing words
            existing_words = []
            for i in range(category_start + 1, category_end):
                line = lines[i].strip()
                if line.startswith('"') and line.endswith('",'):
                    existing_words.append(line[1:-2])  # Remove quotes and comma
            
            # Add new words that don't already exist
            added_count = 0
            for word in words:
                if word not in existing_words:
                    lines.insert(category_end, f'    "{word}",\n')
                    category_end += 1  # Increment end position for each added word
                    added_count += 1
            
            # Write the file back
            with open("c:/Users/<USER>/Downloads/femboycord/wordlist_gen.py", "w", encoding="utf-8") as f:
                f.writelines(lines)
            
            self.log(f"✓ Added {added_count} new words to {category}")
            
            # Clear the entry
            self.bulk_words_entry.delete("1.0", tk.END)
            
            # Update the display
            self.update_category_words_display()
            
        except Exception as e:
            self.log(f"✗ Error bulk adding words to wordlist_gen.py: {str(e)}", True)
    
    def delete_selected_word(self):
        """Delete the selected word from the category"""
        try:
            # Get the selected line
            selected_line = self.category_words_display.index(tk.CURRENT).split('.')[0]
            if not selected_line:
                self.log("✗ Error: No word selected.", True)
                return
            
            # Get the line number (1-based index)
            line_number = int(selected_line)
            
            # Get the category
            category = self.custom_category_var.get()
            
            # Import the module
            import wordlist_gen
            import importlib
            importlib.reload(wordlist_gen)  # Reload to get any changes
            
            # Get the list of words for the selected category
            words = getattr(wordlist_gen, category, [])
            
            if line_number <= 0 or line_number > len(words):
                self.log("✗ Error: Invalid word selection.", True)
                return
            
            # Get the word to delete
            word_to_delete = words[line_number - 1]
            
            # Read the current file
            with open("c:/Users/<USER>/Downloads/femboycord/wordlist_gen.py", "r", encoding="utf-8") as f:
                lines = f.readlines()
            
            # Find the category in the file
            category_start = -1
            category_end = -1
            for i, line in enumerate(lines):
                if line.strip().startswith(f"{category} = ["):
                    category_start = i
                elif category_start != -1 and line.strip().startswith("]"):
                    category_end = i
                    break
            
            if category_start == -1 or category_end == -1:
                self.log(f"✗ Error: Could not find {category} in wordlist_gen.py", True)
                return
            
            # Find and remove the word
            for i in range(category_start + 1, category_end):
                if f'"{word_to_delete}"' in lines[i]:
                    lines.pop(i)
                    break
            
            # Write the file back
            with open("c:/Users/<USER>/Downloads/femboycord/wordlist_gen.py", "w", encoding="utf-8") as f:
                f.writelines(lines)
            
            self.log(f"✓ Deleted '{word_to_delete}' from {category}")
            
            # Update the display
            self.update_category_words_display()
            
        except Exception as e:
            self.log(f"✗ Error deleting word: {str(e)}", True)
    
    def clear_category(self):
        """Clear all words from the selected category"""
        category = self.custom_category_var.get()
        
        try:
            # Read the current file
            with open("c:/Users/<USER>/Downloads/femboycord/wordlist_gen.py", "r", encoding="utf-8") as f:
                lines = f.readlines()
            
            # Find the category in the file
            category_start = -1
            category_end = -1
            for i, line in enumerate(lines):
                if line.strip().startswith(f"{category} = ["):
                    category_start = i
                elif category_start != -1 and line.strip().startswith("]"):
                    category_end = i
                    break
            
            if category_start == -1 or category_end == -1:
                self.log(f"✗ Error: Could not find {category} in wordlist_gen.py", True)
                return
            
            # Remove all words between start and end
            new_lines = lines[:category_start + 1] + lines[category_end:]
            
            # Write the file back
            with open("c:/Users/<USER>/Downloads/femboycord/wordlist_gen.py", "w", encoding="utf-8") as f:
                f.writelines(new_lines)
            
            self.log(f"✓ Cleared all words from {category}")
            
            # Update the display
            self.update_category_words_display()
            
        except Exception as e:
            self.log(f"✗ Error clearing category: {str(e)}", True)
    
    def generate_sample_phrases(self):
        """Generate and display sample phrases without saving to a file"""
        try:
            # Import the module
            import wordlist_gen
            import importlib
            importlib.reload(wordlist_gen)  # Reload to get any changes
            
            # Generate 10 sample phrases
            phrases = [wordlist_gen.generate_aggressive_phrase() for _ in range(10)]
            
            # Create a popup window to display the phrases
            sample_window = tk.Toplevel(self.root)
            sample_window.title("Sample Phrases")
            sample_window.geometry("600x400")
            
            # Add a text widget to display the phrases
            text = tk.Text(sample_window, wrap=tk.WORD)
            text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # Add a scrollbar
            scrollbar = ttk.Scrollbar(text, command=text.yview)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            text.config(yscrollcommand=scrollbar.set)
            
            # Insert the phrases
            for i, phrase in enumerate(phrases):
                text.insert(tk.END, f"{i+1}. {phrase}\n\n")
            
            # Add a close button
            close_btn = ttk.Button(sample_window, text="Close", command=sample_window.destroy)
            close_btn.pack(pady=10)
            
            self.log(f"✓ Generated {len(phrases)} sample phrases")
            
        except Exception as e:
            self.log(f"✗ Error generating sample phrases: {str(e)}", True)
    
    def generate_wordlist(self):
        """Generate a wordlist using wordlist_gen.py"""
        try:
            # Get the settings
            count = self.wordlist_gen_count_var.get()
            output_file = self.wordlist_gen_output_var.get().strip()
            
            if not output_file:
                output_file = self.mandem_wordlist_file
                self.wordlist_gen_output_var.set(output_file)
            
            # Import the module
            import wordlist_gen
            import importlib
            importlib.reload(wordlist_gen)  # Reload to get any changes
            
            # Generate the wordlist
            phrases = wordlist_gen.generate_wordlist(count, output_file)
            
            # Update the AutoBeef wordlist file
            self.mandem_wordlist_file = output_file
            
            # Reload the wordlist
            self.load_mandem_wordlist()
            
            self.log(f"✓ Generated {len(phrases)} phrases and saved to '{output_file}'")
            
        except Exception as e:
            self.log(f"✗ Error generating wordlist: {str(e)}", True)
        
    def toggle_message_collector(self):
        if self.message_collector_active:
            # Stop message collector
            self.message_collector_active = False
            self.message_collector_thread_stop_event.set()
            self.collector_toggle_btn.config(text="Start Collecting Messages")
            self.collector_status_var.set("Status: Idle")
            self.log("Message collector stopped.")
        else:
            # Start message collector
            if not self.validate_token():
                self.log("✗ Error: Invalid token format, cannot start message collector.", True)
                return
                
            channel_id = self.collector_channel_entry.get().strip()
            if not channel_id:
                self.log("✗ Error: Missing channel ID, cannot start message collector.", True)
                return
            
            # Update wordlist file from entry
            self.mandem_wordlist_file = self.collector_wordlist_entry.get().strip()
            if not self.mandem_wordlist_file:
                self.mandem_wordlist_file = "autobeefwordlist.txt"
                self.collector_wordlist_entry.delete(0, tk.END)
                self.collector_wordlist_entry.insert(0, self.mandem_wordlist_file)
            
            self.message_collector_active = True
            self.message_collector_channel_id = channel_id
            self.message_collector_comma_separated = self.collector_comma_var.get()
            self.message_collector_thread_stop_event.clear()
            
            # Get the latest message ID to start from
            self.message_collector_last_message_id = self.get_latest_message_id(channel_id)
            
            # Start the message collector thread
            if not hasattr(self, 'message_collector_thread') or not self.message_collector_thread or not self.message_collector_thread.is_alive():
                self.message_collector_thread = Thread(target=self.message_collector_loop, daemon=True)
                self.message_collector_thread.start()
            
            self.collector_toggle_btn.config(text="Stop Collecting Messages")
            self.collector_status_var.set("Status: Collecting messages...")
            self.log("Message collector started. Monitoring channel for your messages...")
    
    def message_collector_loop(self):
        self.log("Message collector thread started.")
        collected_count = 0
        
        while self.running and not self.message_collector_thread_stop_event.is_set():
            try:
                if not self.message_collector_active:
                    break
                
                # Check if we need to wait due to rate limiting
                if self.rate_limited:
                    sleep_time = max(2, self.rate_limit_reset - time.time())
                    if sleep_time > 0:
                        time.sleep(sleep_time)
                    self.rate_limited = False
                
                # Get new messages from the channel
                new_messages = self.get_new_messages(self.message_collector_channel_id, self.message_collector_last_message_id)
                
                if new_messages:
                    # Filter messages sent by the user
                    user_id = self.get_user_id()
                    if not user_id:
                        self.log("✗ Error: Could not get user ID. Retrying...", True)
                        time.sleep(5)
                        continue
                    
                    user_messages = [msg for msg in new_messages if msg.get('author', {}).get('id') == user_id]
                    
                    if user_messages:
                        # Update the last message ID
                        self.message_collector_last_message_id = new_messages[0].get('id')
                        
                        # Add messages to wordlist
                        for msg in user_messages:
                            content = msg.get('content', '').strip()
                            if content:
                                self.add_message_to_wordlist(content)
                                collected_count += 1
                                self.collector_status_var.set(f"Status: Collected {collected_count} messages")
                
                # Wait a bit before checking again
                time.sleep(2)
                
            except Exception as e:
                self.log(f"✗ Error in message collector: {str(e)}", True)
                time.sleep(5)  # Wait a bit before retrying
        
        self.log("Message collector thread stopped.")
    
    def get_user_id(self):
        """Get the user ID of the token owner"""
        try:
            response = requests.get(
                "https://discord.com/api/v9/users/@me",
                headers=self.get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json().get('id')
            else:
                self.log(f"✗ Error getting user ID: {response.status_code}", True)
                return None
        except Exception as e:
            self.log(f"✗ Error getting user ID: {str(e)}", True)
            return None
    
    def get_new_messages(self, channel_id, last_message_id=None):
        """Get new messages from a channel, optionally after a specific message ID"""
        try:
            url = f"https://discord.com/api/v9/channels/{channel_id}/messages?limit=50"
            if last_message_id:
                url += f"&after={last_message_id}"
                
            response = requests.get(
                url,
                headers=self.get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                messages = response.json()
                # Sort messages by timestamp (newest first)
                messages.sort(key=lambda msg: msg.get('id', '0'), reverse=True)
                return messages
            elif response.status_code == 429:
                retry_after = response.json().get('retry_after', 5)
                self.rate_limited = True
                self.rate_limit_reset = time.time() + retry_after
                self.log(f"⚠ Rate limited while fetching messages - waiting {retry_after:.1f}s", True)
                return []
            else:
                self.log(f"✗ Error fetching messages: {response.status_code}", True)
                return []
        except Exception as e:
            self.log(f"✗ Error fetching messages: {str(e)}", True)
            return []
    
    def add_message_to_wordlist(self, message):
        """Add a message to the wordlist file"""
        try:
            # Load existing wordlist
            existing_messages = []
            try:
                with open(self.mandem_wordlist_file, 'r', encoding='utf-8') as f:
                    existing_messages = [line.strip() for line in f.readlines()]
            except FileNotFoundError:
                # File doesn't exist yet, will create it
                pass
            
            # Format the message based on settings
            if self.message_collector_comma_separated:
                # Split message by newlines and join with commas
                formatted_message = message.replace('\n', ', ')
            else:
                # Keep message as is
                formatted_message = message
            
            # Add the message if it's not already in the list
            if formatted_message not in existing_messages:
                with open(self.mandem_wordlist_file, 'a', encoding='utf-8') as f:
                    f.write(formatted_message + '\n')
                
                # Also add to the in-memory wordlist if it's loaded
                if formatted_message not in self.mandem_wordlist:
                    self.mandem_wordlist.append(formatted_message)
                    
                    # Update the wordlist preview if it exists
                    if hasattr(self, 'mandem_wordlist_preview') and self.mandem_wordlist_preview:
                        self.mandem_wordlist_preview.config(state=tk.NORMAL)
                        if self.mandem_wordlist_preview.get("1.0", tk.END).strip():
                            self.mandem_wordlist_preview.insert(tk.END, "\n")
                        self.mandem_wordlist_preview.insert(tk.END, formatted_message)
                        self.mandem_wordlist_preview.config(state=tk.DISABLED)
                
                self.log(f"Added new message to wordlist: {formatted_message[:30]}{'...' if len(formatted_message) > 30 else ''}")
        except Exception as e:
            self.log(f"✗ Error adding message to wordlist: {str(e)}", True)


    def handle_token_input(self, *args):
        current_text = self.token_var.get()
        
        if not self.bearer_added and current_text and not current_text.startswith("Bearer "):
            self.bearer_added = True
            self.token_var.set("Bearer " + current_text)
            self.token_entry.icursor(len(current_text))
        elif self.bearer_added and not current_text:
            self.bearer_added = False

    def get_clean_token(self: object) -> str:
        full_token = self.token_var.get()
        if full_token.startswith("Bearer "):
            return full_token[7:]
        return full_token

    def setup_hotkeys(self):
        self.message_entry.bind("<Return>", lambda e: "break" if self.queue_message() else "break")
        self.message_entry.bind("<Shift-Return>", self.insert_single_newline)
        self.root.bind("<F1>", lambda e: self.toggle_prefix_suffix())
        self.root.bind("<F2>", lambda e: self.toggle_space_split())
        self.root.bind("<F3>", lambda e: self.toggle_gc_name_changer_mode())
        self.root.bind("<F4>", lambda e: self.toggle_queue_pause())
        self.root.bind("<F5>", lambda e: self.set_reply_to_last_message())
        self.root.bind("<Escape>", lambda e: self.toggle_mandem_mode())
        self.root.bind("<Control-Escape>", lambda e: self.clear_reply_target())

    def insert_single_newline(self, event):
        self.message_entry.insert(tk.INSERT, "\n")
        return "break"

    def toggle_gc_name_changer_mode(self):
        self.gc_name_changer_mode = not self.gc_name_changer_mode
        self.gc_name_changer_var_ui.set(1 if self.gc_name_changer_mode else 0)
        self.gc_name_changer_var_menu.set(1 if self.gc_name_changer_mode else 0)
        if self.gc_name_changer_mode:
            self.root.after(0, lambda: self.message_input_label.config(text="New Group Name:"))
            self.log("GC Name Changer Mode: ENABLED. Enter text to set group name.")
        else:
            self.root.after(0, lambda: self.message_input_label.config(text="Message:"))
            self.log("GC Name Changer Mode: DISABLED. Enter text to send messages.")
        self.stop_typing_indicator(manual_stop=True)
        self.update_menu_states() 

    def toggle_gc_name_changer_mode_ui(self):
        self.toggle_gc_name_changer_mode()

    def toggle_space_split(self):
        self.space_split_enabled = not self.space_split_enabled
        self.space_split_var_ui.set(1 if self.space_split_enabled else 0)
        self.space_split_var_menu.set(1 if self.space_split_enabled else 0)
        self.log(f"Space splitting {'ENABLED' if self.space_split_enabled else 'DISABLED'}")

    def toggle_space_split_ui(self):
        self.toggle_space_split()

    def toggle_prefix_suffix(self):
        self.prefix = self.prefix_entry.get()
        self.suffix = self.suffix_entry.get()
        
        status = "ON" if self.prefix or self.suffix else "OFF"
        self.log(f"Prefix/Suffix {status}: '{self.prefix}' / '{self.suffix}'")
        self.prefix_suffix_var.set(bool(self.prefix or self.suffix))

    def start_animation(self):
        # Modern loading animation characters
        if not hasattr(self, 'animation_frames'):
            self.animation_frames = ["⣾", "⣽", "⣻", "⢿", "⡿", "⣟", "⣯", "⣷"]
            self.animation_index = 0
        
        if self._is_sending_message: 
            self.root.after(0, lambda: self.status_bar.config(text=f"{self.animation_frames[self.animation_index]} Sending... | Queue: {self.message_queue.qsize()}"))
            self.animation_index = (self.animation_index + 1) % len(self.animation_frames)
        else:
             self.root.after(0, lambda: self.status_bar.config(text=self.status_var.get()))
        self.root.after(80, self.start_animation)  # Slightly faster animation

    def apply_smart_bold(self, message):
        try:
            self.smart_bold_threshold = int(self.bold_threshold_entry.get())
            if self.smart_bold_threshold > 0 and len(message.strip()) <= self.smart_bold_threshold:
                return f"# {message.strip()}" 
        except ValueError:
            pass 
        return message

    def _get_cased_correction(self, matched_text: str, correction_text: str) -> str:
        """
        Applies the capitalization pattern of matched_text to correction_text.
        Prioritizes ALL_CAPS and Title Case. Otherwise, attempts character-by-character
        casing copy.
        """
        if not matched_text:
            return correction_text # Cannot determine casing if no match text

        if matched_text.isupper():
            return correction_text.upper()
        elif matched_text.istitle():
            return correction_text.title()
        
        # Fallback to character-by-character casing for mixed or original case
        result = []
        for i, char in enumerate(correction_text):
            if i < len(matched_text) and matched_text[i].isupper():
                result.append(char.upper())
            else:
                result.append(char.lower()) # Default to lowercase if no matching uppercase in matched_text
        return "".join(result)

    def apply_corrections(self, message):
        corrected_message = message
        modified = False

        sorted_typos = sorted(self.typo_corrections.keys(), key=len, reverse=True)

        for typo_lower in sorted_typos:
            correction_original_case = self.typo_corrections[typo_lower]

            pattern = r'\b' + re.escape(typo_lower) + r'\b'
            
            def replacer(match):
                nonlocal modified
                modified = True
                matched_text = match.group(0) 
                return self._get_cased_correction(matched_text, correction_original_case)

            corrected_message = re.sub(pattern, replacer, corrected_message, flags=re.IGNORECASE)
            
        if modified:
            self.log(f"✓ Corrected typos: '{message[:40]}...' -> '{corrected_message[:40]}...'")
        return corrected_message

    def format_message(self, message, is_multitoken=False):
        # Apply corrections first
        message = self.apply_corrections(message)
        
        # Smart bold applies to the whole message, not per line
        message = self.apply_smart_bold(message) 

        # Only apply space split in non-multitoken mode
        if not is_multitoken and self.space_split_enabled:
            message = re.sub(r'\s+', '\n', message.strip())
        
        # Apply prefix and suffix last
        if is_multitoken:
            # Use multitoken prefix/suffix entries
            prefix = self.multitoken_prefix_entry.get() if hasattr(self, 'multitoken_prefix_entry') else ""
            suffix = self.multitoken_suffix_entry.get() if hasattr(self, 'multitoken_suffix_entry') else ""
            return f"{prefix}{message}{suffix}"
        else:
            # Use regular prefix/suffix entries
            return f"{self.prefix_entry.get()}{message}{self.suffix_entry.get()}"

    def log(self, message, is_error=False):
        self.root.after(0, self._log_ui_update, message, is_error)

    def _log_ui_update(self, message, is_error):
        self.log_text.config(state=tk.NORMAL)
        color = self.error_color if is_error else (self.highlight_color if "✓" in message else "#AAAAAA") 
        
        tag_name = "log_color_tag"
        self.log_text.tag_config(tag_name, foreground=color) 
        self.log_text.insert(tk.END, f"{message}\n", (tag_name,)) 
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.update_status()

    def update_status(self):
        self.root.after(0, self._update_status_ui)

    def _update_status_ui(self):
        queue_size = self.message_queue.qsize()
        status = f"Ready | Queue: {queue_size}"
        if self.rate_limited:
            remaining = max(0, self.rate_limit_reset - time.time())
            status = f"Rate Limited ({remaining:.1f}s) | Queue: {queue_size}"
        elif self._is_sending_message: 
            status = f"Sending... | Queue: {queue_size}"
        elif self.queue_paused:
            status = f"PAUSED | Queue: {queue_size}"
        
        mode_indicators = []
        if self.gc_name_changer_mode:
            mode_indicators.append("GC Name Mode")
        if self.queue_paused:
            mode_indicators.append("Queue Paused")

        if mode_indicators:
            self.status_var.set(f"{status} ({', '.join(mode_indicators)})")
        else:
            self.status_var.set(status)


    def clear_queue(self):
        while not self.message_queue.empty():
            self.message_queue.get()
        self.log("✓ Queue cleared")
        self.update_status() 

    def validate_token(self):
        token = self.token_var.get().strip()
        return len(token) >= 30

    def queue_message(self):
        input_text = self.message_entry.get("1.0", tk.END).strip()
        if not input_text:
            self.log("✗ Message is empty", True)
            return False
        
        channel_id = self.channel_entry.get().strip()
        if not channel_id:
            self.log("✗ Channel ID is empty", True)
            return False
        
        if not self.validate_token():
            self.log("✗ Token is invalid", True)
            return False
        
        # Get reply target if any
        reply_to_id = self.get_reply_target()
        
        if self.gc_name_changer_mode:
            new_name = input_text
            self.message_queue.put(("NAME_CHANGE", channel_id, new_name))
            self.log(f"✓ Queued name change to: '{new_name}'")
        else:
            formatted_message = self.format_message(input_text)
            # Include reply_to_id in the queue item
            self.message_queue.put(("MESSAGE", channel_id, formatted_message, reply_to_id))
            reply_info = f" (Reply to: {reply_to_id[:8]}...)" if reply_to_id else ""
            self.log(f"✓ Queued{reply_info}: {formatted_message[:60]}{'...' if len(formatted_message) > 60 else ''}")
        
        self.message_entry.delete("1.0", tk.END)
        self.stop_typing_indicator(manual_stop=True) 
        self.update_status() 
        return True

    def get_headers(self):
        return {
            "Authorization": self.token_var.get().strip(),
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "X-Super-Properties": base64.b64encode(json.dumps({
                "os": "Windows",
                "browser": "Chrome",
                "device": "",
                "system_locale": "en-US",
                "browser_user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "browser_version": "91.0.4472.124",
                "os_version": "10",
                "referrer": "",
                "referring_domain": "",
                "referrer_current": "",
                "referring_domain_current": "",
                "release_channel": "stable",
                "client_build_number": 100000,
                "client_event_source": None
            }).encode()).decode(),
        }

    def send_message(self, channel_id, message, reply_to_id=None):
        try:
            # Create base payload
            payload = {
                "content": message,
                "nonce": str(int(time.time()*1000)),
                "tts": False
            }
            
            # Add message_reference for replies
            if reply_to_id:
                payload["message_reference"] = {
                    "message_id": reply_to_id,
                    "channel_id": channel_id,
                    "fail_if_not_exists": False
                }
            
            # Debug log for payload
            self.log(f"Debug - Sending payload: {str(payload)[:100]}...")
            
            response = requests.post(
                f"https://discord.com/api/v9/channels/{channel_id}/messages",
                headers=self.get_headers(),
                json=payload,
                timeout=10
            )
            
            # Handle response
            if response.status_code == 200:
                response_data = response.json()
                message_id = response_data.get('id')
                is_reply = 'referenced_message' in response_data
                reply_status = " (as reply)" if is_reply else ""
                self.log(f"✓ Sent{reply_status}: {message[:60]}{'...' if len(message) > 60 else ''}")
                return True, None, message_id
            elif response.status_code == 401:
                self.log("✗ 401 Unauthorized - Invalid token", True)
                return False, "permanent", None
            elif response.status_code == 403:
                self.log("✗ 403 Forbidden - No permission to send messages in this channel", True)
                return False, "permanent", None
            elif response.status_code == 429:
                retry_after = response.json().get('retry_after', 5)
                self.rate_limited = True
                self.rate_limit_reset = time.time() + retry_after
                self.log(f"⚠ Rate limited - waiting {retry_after:.1f}s", True)
                return False, "rate_limit", None
            else:
                self.log(f"✗ Error {response.status_code}: {response.text[:200]}", True)
                return False, "temporary", None
        except Exception as e:
            self.log(f"✗ Error sending message: {str(e)}", True)
            return False, "temporary", None

    def change_group_name(self, channel_id, new_name):
        try:
            headers = self.get_headers()
            headers["Content-Type"] = "application/json"
            
            payload = {
                "name": new_name
            }
            
            response = requests.patch(
                f"https://discord.com/api/v9/channels/{channel_id}",
                headers=headers,
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                self.log(f"✓ Successfully changed group name to: '{new_name}'")
                return True, None
            elif response.status_code == 401:
                self.log("✗ 401 Unauthorized - Invalid token for name change", True)
                return False, "permanent"
            elif response.status_code == 403:
                self.log("✗ 403 Forbidden - No permission to change group name", True)
                return False, "permanent"
            elif response.status_code == 404:
                self.log("✗ 404 Not Found - Channel not found or not a group DM for name change", True)
                return False, "permanent"
            elif response.status_code == 429:
                retry_after = response.json().get('retry_after', 5)
                self.rate_limited = True
                self.rate_limit_reset = time.time() + retry_after
                self.log(f"⚠ Rate limited (name change) - waiting {retry_after:.1f}s", True)
                return False, "rate_limit"
            else:
                error_msg = response.text[:200] + "..." if len(response.text) > 200 else response.text
                self.log(f"✗ Error {response.status_code}: {error_msg} (name change)", True)
                return False, "temporary"
                
        except requests.exceptions.Timeout:
            self.log(f"✗ Request timed out when changing group name for {channel_id}", True)
            return False, "temporary"
        except requests.exceptions.ConnectionError:
            self.log(f"✗ Connection error when changing group name for {channel_id}", True)
            return False, "temporary"
        except Exception as e:
            self.log(f"✗ Unexpected error changing group name: {str(e)}", True)
            return False, "temporary"

    def send_typing_indicator(self, channel_id):
        url = f"https://discord.com/api/v9/channels/{channel_id}/typing"
        headers = self.get_headers()
        try:
            response = requests.post(url, headers=headers, timeout=5)
            if response.status_code == 204:
                pass
            elif response.status_code == 401:
                self.log("✗ Typing Indicator Error: 401 Unauthorized - Invalid token. Stopping typing.", True)
                self.root.after(0, self.stop_typing_indicator, manual_stop=True)
            elif response.status_code == 403:
                self.log("✗ Typing Indicator Error: 403 Forbidden - No permission to type in this channel. Stopping typing.", True)
                self.root.after(0, self.stop_typing_indicator, manual_stop=True)
            elif response.status_code == 404:
                self.log("✗ Typing Indicator Error: 404 Not Found - Channel does not exist. Stopping typing.", True)
                self.root.after(0, self.stop_typing_indicator, manual_stop=True)
            elif response.status_code == 429:
                retry_after = response.json().get('retry_after', 5)
                self.log(f"⚠ Typing Indicator Rate Limited: waiting {retry_after:.1f}s", True)
            else:
                self.log(f"✗ Typing Indicator Error {response.status_code}: {response.text}", True)
        except requests.exceptions.Timeout:
            self.log(f"✗ Typing Indicator Network Error: Request timed out for {channel_id}", True)
        except requests.exceptions.ConnectionError:
            self.log(f"✗ Typing Indicator Network Error: Connection failed for {channel_id}", True)
        except Exception as e:
            self.log(f"✗ Typing Indicator Unexpected Error: {str(e)}", True)

    def typing_loop(self):
        self.log("Typing loop thread started.")
        while self.running and not self.typing_thread_stop_event.is_set():
            try:
                if self.typing_active:
                    channel_id = self.channel_entry.get().strip()
                    token_valid = self.validate_token()
                    
                    if not channel_id or not token_valid:
                        self.log("Typing loop: Invalid channel ID or token. Stopping typing.", True)
                        self.root.after(0, self.stop_typing_indicator, manual_stop=True)
                        break
                    
                    if not self.gc_name_changer_mode:
                        self.send_typing_indicator(channel_id)
                    else:
                        self.root.after(0, self.stop_typing_indicator, manual_stop=True)
                        break

                self.typing_thread_stop_event.wait(8)
            except Exception as e:
                self.log(f"✗ Critical error in typing loop: {str(e)}", True)
                self.root.after(0, self.stop_typing_indicator, manual_stop=True)
                break
        self.log("Typing loop thread stopped.")


    def start_typing_indicator(self):
        if self.gc_name_changer_mode:
            self.log("Cannot start typing indicator in GC Name Changer Mode.", False)
            return

        channel_id = self.channel_entry.get().strip()
        if not channel_id:
            self.log("Cannot start typing indicator: Channel ID is empty.", True)
            return
        if not self.validate_token():
            self.log("Cannot start typing indicator: Token is invalid.", True)
            return

        if not self.typing_active:
            self.log("Attempting to start typing indicator...", False)
            self.typing_active = True
            self.typing_thread_stop_event.clear()
            if self.typing_thread and self.typing_thread.is_alive():
                self.log("Typing thread already running.", False)
            else:
                self.typing_thread = Thread(target=self.typing_loop, daemon=True)
                self.typing_thread.start()
                self.log("Typing indicator thread initiated.")
            
            self.send_typing_indicator(channel_id)
            self.log("Typing indicator activated.")

    def stop_typing_indicator(self, event=None, manual_stop=False):
        if self.typing_active:
            self.log(f"Stopping typing indicator (triggered by {'manual action' if manual_stop else 'inactivity/focus change'})...", False)
            self.typing_active = False
            self.typing_thread_stop_event.set()
            
            if self.typing_timer:
                self.root.after_cancel(self.typing_timer)
                self.typing_timer = None
            
            self.log("Typing indicator stopped.")
        else:
            pass


    def on_key_press_for_typing(self, event):
        if event.keysym in ("Shift_L", "Shift_R", "Control_L", "Control_R", "Alt_L", "Alt_R", 
                            "Up", "Down", "Left", "Right", "Meta_L", "Meta_R", "Caps_Lock", "Tab"):
            return

        if self.gc_name_changer_mode:
            self.stop_typing_indicator()
            return
        if not self.validate_token():
            self.log("Key press ignored: Token is invalid.", False)
            self.stop_typing_indicator()
            return
        if not self.channel_entry.get().strip():
            self.log("Key press ignored: Channel ID is empty.", False)
            self.stop_typing_indicator()
            return

        if not self.typing_active:
            self.start_typing_indicator()

        self.last_key_press_time = time.time()
        
        if self.typing_timer:
            self.root.after_cancel(self.typing_timer)
        
        self.typing_timer = self.root.after(3000, self.check_inactivity_and_stop_typing)
        
    def check_inactivity_and_stop_typing(self):
        if (time.time() - self.last_key_press_time) >= 2.9:
            self.stop_typing_indicator()

    def check_rate_limited_tokens(self):
        """Check and reset rate-limited tokens after the reset time has passed"""
        current_time = time.time()
        tokens_to_reset = []
        
        # Find tokens that can be reset
        for token, timestamp in list(self.multitoken_rate_limited_tokens.items()):
            if current_time - timestamp >= self.multitoken_rate_limit_reset_time:
                tokens_to_reset.append(token)
        
        # Reset tokens
        for token in tokens_to_reset:
            del self.multitoken_rate_limited_tokens[token]
            self.log(f"Token reset from rate-limited status after {self.multitoken_rate_limit_reset_time}s")
            
        # Log if tokens were reset
        if tokens_to_reset:
            self.log(f"Reset {len(tokens_to_reset)} rate-limited tokens")
            
    def load_corrections(self):
        self.typo_corrections = {}
        try:
            if not os.path.exists(self.corrections_file):
                with open(self.corrections_file, 'w', encoding='utf-8') as f:
                    f.write("# Add your corrections here, one per line.\n")
                    f.write("# Format: typo : correction\n")
                    f.write("# Example: recieve : receive\n")
                self.log(f"Created empty '{self.corrections_file}' for corrections.")
                self.update_corrections_display()
                return

            with open(self.corrections_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    
                    parts = line.split(':', 1)
                    if len(parts) == 2:
                        typo = parts[0].strip().lower() 
                        correction = parts[1].strip() 
                        if typo and correction:
                            self.typo_corrections[typo] = correction
            self.log(f"✓ Loaded {len(self.typo_corrections)} corrections from '{self.corrections_file}'.")
        except Exception as e:
            self.log(f"✗ Error loading corrections from '{self.corrections_file}': {str(e)}", True)
        self.update_corrections_display()

    def save_corrections(self):
        try:
            with open(self.corrections_file, 'w', encoding='utf-8') as f:
                f.write("# Add your corrections here, one per line.\n")
                f.write("# Format: typo : correction\n")
                f.write("# Example: recieve : receive\n")
                for typo_lower, correction_original_case in self.typo_corrections.items():
                    f.write(f"{typo_lower} : {correction_original_case}\n")
            self.log(f"✓ Saved {len(self.typo_corrections)} corrections to '{self.corrections_file}'.")
        except Exception as e:
            self.log(f"✗ Error saving corrections to '{self.corrections_file}': {str(e)}", True)
        self.update_corrections_display()

    def add_correction_from_entries(self):
        typo_word = self.typo_word_entry.get().strip()
        correction = self.correction_entry.get().strip()

        if not typo_word or not correction:
            self.log("✗ Both 'Word (typo)' and 'Correction' fields must be filled to add a correction.", True)
            return
        
        self.typo_corrections[typo_word.lower()] = correction 
        self.save_corrections()
        self.log(f"✓ Added/Updated correction: '{typo_word}' -> '{correction}'")
        self.typo_word_entry.delete(0, tk.END)
        self.correction_entry.delete(0, tk.END)
        self.typo_word_entry.focus_set()

    def update_corrections_display(self):
        self.current_corrections_text.config(state=tk.NORMAL)
        self.current_corrections_text.delete("1.0", tk.END)
        if not self.typo_corrections:
            self.current_corrections_text.insert(tk.END, "No corrections loaded.")
        else:
            for typo_lower, correction_original_case in self.typo_corrections.items():
                self.current_corrections_text.insert(tk.END, f"{typo_lower} : {correction_original_case}\n")
        self.current_corrections_text.config(state=tk.DISABLED)

    def start_message_sender(self):
        if not hasattr(self, 'sender_thread') or not self.sender_thread.is_alive():
            self.sender_thread = Thread(target=self._message_sender_loop, daemon=True)
            self.sender_thread.start()
            self.log("Message sender thread started.")
        else:
            self.log("Message sender thread is already running.")

    def _message_sender_loop(self):
        current_item = None 
        last_send_time = 0
        double_send_chance = 0.7  # 30% chance to send double messages when possible

        while self.running:
            try:
                if self.rate_limited:
                    sleep_time = max(2.0, self.rate_limit_reset - time.time()) 
                    if sleep_time > 0:
                        self.log(f"Rate Limited: Pausing for {sleep_time:.2f}s", is_error=True)
                        time.sleep(sleep_time)
                    self.rate_limited = False 
                    self.update_status() 

                if self.queue_paused:
                    self._is_sending_message = False
                    self.update_status()
                    time.sleep(0.1)
                    continue

                # Calculate time to wait based on configured delay
                current_time = time.time()
                
                # Check if manual burst mode is enabled - this takes priority over all other modes
                if self.manual_burst_enabled:
                    # Check if we should enter or exit burst mode
                    if not self.in_burst_mode and (current_time - self.last_burst_time) >= self.manual_burst_interval:
                        # Enter burst mode
                        self.in_burst_mode = True
                        self.last_burst_time = current_time
                        self.log("Entering burst mode! Sending doubled messages with zero delay...", False)
                        # We don't actually pause the queue during burst mode anymore
                        # Instead, we'll process it faster with doubled messages
                    elif self.in_burst_mode and (current_time - self.last_burst_time) >= self.manual_burst_duration:
                        # Exit burst mode
                        self.in_burst_mode = False
                        self.log("Exiting burst mode. Returning to normal speed.", False)
                    
                    # Use appropriate delay based on current mode
                    if self.in_burst_mode:
                        # Force zero delay during burst for immediate sending
                        delay = 0.0
                        # Force time_since_last_send to be large to bypass delay check
                        time_since_last_send = 999999.0
                        # Disable any other delay mechanisms during burst
                        self.burst_count = 0  # Reset burst count to prevent stacking
                    else:
                        delay = self.manual_burst_delay  # Use configured manual delay
                else:
                    # Use normal delay from slider
                    delay = self.base_send_delay.get()
                
                time_since_last_send = current_time - last_send_time
                
                if time_since_last_send < delay:
                    time.sleep(max(0.1, delay - time_since_last_send))
                    continue

                if current_item is None:
                    try:
                        # Get item from queue, now with possible reply_to_id
                        queue_item = self.message_queue.get(timeout=0.5)
                        # Handle both old format (3 items) and new format (4 items with reply_to_id)
                        if len(queue_item) == 3:
                            item_type, channel_id, data = queue_item
                            reply_to_id = None
                        else:
                            item_type, channel_id, data, reply_to_id = queue_item
                        current_item = (item_type, channel_id, data, reply_to_id)
                        
                        # If in manual burst mode and actively bursting, immediately get a second message
                        # to send doubled messages with zero delay
                        if self.manual_burst_enabled and self.in_burst_mode and not self.message_queue.empty():
                            try:
                                # Store the first message
                                first_item = current_item
                                
                                # Get a second message
                                second_queue_item = self.message_queue.get(block=False)
                                if len(second_queue_item) == 3:
                                    second_item_type, second_channel_id, second_data = second_queue_item
                                    second_reply_to_id = None
                                else:
                                    second_item_type, second_channel_id, second_data, second_reply_to_id = second_queue_item
                                
                                # If it's a message (not a name change), send it immediately
                                if second_item_type == "MESSAGE":
                                    self.log("Sending doubled burst message", False)
                                    # Send the second message in parallel without waiting
                                    Thread(
                                        target=self.send_message,
                                        args=(second_channel_id, second_data, second_reply_to_id),
                                        daemon=True
                                    ).start()
                            except Empty:
                                pass  # No second message available, continue with just the first one
                    except Empty:
                        self._is_sending_message = False 
                        self.update_status()
                        time.sleep(0.1) 
                        continue 

                self._is_sending_message = True 
                self.update_status()
                
                success = False
                error_type = None 
                sent_message_id = None

                item_type, channel_id, data, reply_to_id = current_item

                if item_type == "MESSAGE":
                    success, error_type, sent_message_id = self.send_message(channel_id, data, reply_to_id)
                elif item_type == "NAME_CHANGE":
                    success, error_type = self.change_group_name(channel_id, data)
                    sent_message_id = None
                
                if success:
                    last_send_time = time.time()
                    current_item = None 
                    
                    # Store the last sent message ID for potential chaining of replies
                    if sent_message_id:
                        self.last_sent_message_id = sent_message_id
                    
                    # Check if we should send a double message - but not if manual burst mode is enabled
                    should_double_send = random.random() < double_send_chance and not self.manual_burst_enabled
                    
                    if should_double_send and not self.message_queue.empty() and not self.rate_limited:
                        try:
                            # Try to immediately send another message
                            next_queue_item = self.message_queue.get(block=False)
                            if len(next_queue_item) == 3:
                                next_item_type, next_channel_id, next_data = next_queue_item
                                next_reply_to_id = None
                            else:
                                next_item_type, next_channel_id, next_data, next_reply_to_id = next_queue_item
                            
                            if next_item_type == "MESSAGE":
                                success, _, _ = self.send_message(next_channel_id, next_data, next_reply_to_id)
                                if success:
                                    self.log("Sent double message", False)
                                else:
                                    # Put it back in the queue if it failed
                                    self.message_queue.put((next_item_type, next_channel_id, next_data, next_reply_to_id))
                        except Empty:
                            pass
                else:
                    if error_type == "temporary":
                        self.log(f"Retrying item due to temporary failure: {data[:50]}...", True)
                        time.sleep(1) 
                    elif error_type == "permanent":
                        current_item = None 
                        self.log(f"Item permanently failed and was NOT re-queued: {data[:50]}...", True)
            
            except Exception as e:
                self.log(f"✗ Critical error in message sender loop: {str(e)}", True)
                self._is_sending_message = False 
                self.update_status()
                time.sleep(2) 
            finally:
                if self.message_queue.empty() and not self.rate_limited and current_item is None:
                    self._is_sending_message = False
                self.update_status()

    def on_closing(self):
        self.log("Application closing down. Attempting to stop threads...")
        self.running = False
        self.typing_thread_stop_event.set()

        if hasattr(self, 'typing_thread') and self.typing_thread and self.typing_thread.is_alive():
            self.typing_thread.join(timeout=1)
            if self.typing_thread.is_alive():
                self.log("Typing thread did not terminate cleanly.", True)
            else:
                self.log("Typing thread stopped.")
        
        if hasattr(self, 'sender_thread') and self.sender_thread and self.sender_thread.is_alive():
            self.sender_thread.join(timeout=1)
            if self.sender_thread.is_alive():
                self.log("Message sender thread did not terminate cleanly.", True)
            else:
                self.log("Message sender thread stopped.")

        self.root.destroy()

    def toggle_queue_pause(self):
        was_paused = self.queue_paused
        self.queue_paused = not self.queue_paused
        
        if self.queue_paused:
            self.log("Queue PAUSED. Press F4 again to resume.")
        else:
            self.log("Queue RESUMED. Messages will be sent with 300ms delay.")
            self._was_paused = was_paused  # Track that we're coming from paused state
        
        self.update_status()

    def setup_multitoken_tab(self, parent):
        # Main layout - split into left and right panes
        paned_window = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        left_pane = ttk.Frame(paned_window)
        right_pane = ttk.Frame(paned_window)
        paned_window.add(left_pane, weight=3)
        paned_window.add(right_pane, weight=1)
        
        # Initialize prefix/suffix variables
        self.multitoken_prefix = ""
        self.multitoken_suffix = ""
        
        # Left pane - channel ID and controls
        channel_frame = ttk.Frame(left_pane)
        channel_frame.pack(fill=tk.X, pady=3)
        ttk.Label(channel_frame, text="Channel ID:", style='Section.TLabel').pack(anchor=tk.W)
        self.multitoken_channel_entry = ttk.Entry(channel_frame, style='TEntry')
        self.multitoken_channel_entry.pack(fill=tk.X, pady=3)
        
        # Token list preview
        token_frame = ttk.LabelFrame(left_pane, text="Loaded Tokens")
        token_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        token_scroll = ttk.Scrollbar(token_frame)
        token_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.multitoken_preview = tk.Text(token_frame, height=10, yscrollcommand=token_scroll.set)
        self.multitoken_preview.pack(fill=tk.BOTH, expand=True)
        token_scroll.config(command=self.multitoken_preview.yview)
        
        # Wordlist preview
        wordlist_frame = ttk.LabelFrame(left_pane, text="Message Wordlist Preview")
        wordlist_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        wordlist_scroll = ttk.Scrollbar(wordlist_frame)
        wordlist_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.multitoken_wordlist_preview = tk.Text(wordlist_frame, height=10, yscrollcommand=wordlist_scroll.set)
        self.multitoken_wordlist_preview.pack(fill=tk.BOTH, expand=True)
        wordlist_scroll.config(command=self.multitoken_wordlist_preview.yview)
        
        # Control buttons
        btn_frame = ttk.Frame(left_pane)
        btn_frame.pack(fill=tk.X, pady=8)
        
        self.multitoken_toggle_btn = ttk.Button(
            btn_frame, 
            text="Start Multitoken Mode", 
            command=self.toggle_multitoken_mode
        )
        self.multitoken_toggle_btn.pack(side=tk.LEFT, padx=3, fill=tk.X, expand=True)
        
        reload_tokens_btn = ttk.Button(
            btn_frame, 
            text="Reload Tokens", 
            command=self.load_multitoken_tokens
        )
        reload_tokens_btn.pack(side=tk.LEFT, padx=3, fill=tk.X, expand=True)
        
        reload_wordlist_btn = ttk.Button(
            btn_frame, 
            text="Reload Wordlist", 
            command=self.load_multitoken_wordlist
        )
        reload_wordlist_btn.pack(side=tk.LEFT, padx=3, fill=tk.X, expand=True)
        
        # Right pane - settings
        settings_frame = ttk.LabelFrame(right_pane, text="Multitoken Settings", padding=8)
        settings_frame.pack(fill=tk.X, pady=5, padx=5)
        
        # Messages until rotation slider
        self.msgs_until_rotation_label = ttk.Label(settings_frame, text=f"Msgs until rotation: {self.multitoken_msgs_until_rotation}")
        self.msgs_until_rotation_label.pack(anchor=tk.W, pady=2)
        
        self.msgs_until_rotation_slider = ttk.Scale(
            settings_frame,
            from_=1, to=30,
            orient=tk.HORIZONTAL,
            value=self.multitoken_msgs_until_rotation,
            command=self.update_msgs_until_rotation
        )
        self.msgs_until_rotation_slider.pack(fill=tk.X, pady=3)
        
        # Note about zero delay
        ttk.Label(settings_frame, text="Message delay: 0ms (instant sending)").pack(anchor=tk.W, pady=5)
        
        # Simultaneous messages settings
        self.simultaneous_var = tk.BooleanVar(value=self.multitoken_simultaneous_enabled)
        self.simultaneous_check = ttk.Checkbutton(
            settings_frame, 
            text="Enable simultaneous messages", 
            variable=self.simultaneous_var,
            command=self.toggle_simultaneous_messages
        )
        self.simultaneous_check.pack(anchor=tk.W, pady=5)
        
        # Simultaneous count slider
        self.simultaneous_count_label = ttk.Label(settings_frame, text=f"Simultaneous count: {self.multitoken_simultaneous_count}")
        self.simultaneous_count_label.pack(anchor=tk.W, pady=2)
        
        self.simultaneous_count_slider = ttk.Scale(
            settings_frame,
            from_=2, to=10,
            orient=tk.HORIZONTAL,
            value=self.multitoken_simultaneous_count,
            command=self.update_simultaneous_count
        )
        self.simultaneous_count_slider.pack(fill=tk.X, pady=3)
        
        # Rate limit reset time slider
        self.rate_limit_reset_label = ttk.Label(settings_frame, text=f"Rate limit reset: {self.multitoken_rate_limit_reset_time}s")
        self.rate_limit_reset_label.pack(anchor=tk.W, pady=2)
        
        self.rate_limit_reset_slider = ttk.Scale(
            settings_frame,
            from_=0.1, to=10,
            orient=tk.HORIZONTAL,
            value=self.multitoken_rate_limit_reset_time,
            command=self.update_rate_limit_reset_time
        )
        self.rate_limit_reset_slider.pack(fill=tk.X, pady=3)
        
        # Spontaneous message chance slider
        self.spontaneous_chance_label = ttk.Label(settings_frame, text=f"Extra spontaneous msg chance: {self.multitoken_spontaneous_chance}%")
        self.spontaneous_chance_label.pack(anchor=tk.W, pady=2)
        
        self.spontaneous_chance_slider = ttk.Scale(
            settings_frame,
            from_=0, to=100,
            orient=tk.HORIZONTAL,
            value=self.multitoken_spontaneous_chance,
            command=self.update_spontaneous_chance
        )
        self.spontaneous_chance_slider.pack(fill=tk.X, pady=3)
        

        
        # Prefix/Suffix settings
        prefix_suffix_frame = ttk.LabelFrame(right_pane, text="Message Formatting", padding=8)
        prefix_suffix_frame.pack(fill=tk.X, pady=5, padx=5)
        
        ttk.Label(prefix_suffix_frame, text="Prefix:").pack(anchor=tk.W, pady=2)
        self.multitoken_prefix_entry = ttk.Entry(prefix_suffix_frame, style='TEntry')
        self.multitoken_prefix_entry.pack(fill=tk.X, pady=3)
        
        ttk.Label(prefix_suffix_frame, text="Suffix:").pack(anchor=tk.W, pady=2)
        self.multitoken_suffix_entry = ttk.Entry(prefix_suffix_frame, style='TEntry')
        self.multitoken_suffix_entry.pack(fill=tk.X, pady=3)
        
        # Status display
        status_frame = ttk.LabelFrame(right_pane, text="Multitoken Status", padding=8)
        status_frame.pack(fill=tk.X, pady=5, padx=5)
        
        # Messages per second counter
        self.multitoken_mps_var = tk.StringVar(value="0.00 msg/s")
        ttk.Label(status_frame, text="Speed:").pack(anchor=tk.W, pady=2)
        ttk.Label(status_frame, textvariable=self.multitoken_mps_var).pack(anchor=tk.W, pady=2)
        
        self.multitoken_current_token_var = tk.StringVar(value="None")
        ttk.Label(status_frame, text="Current Token:").pack(anchor=tk.W, pady=2)
        ttk.Label(status_frame, textvariable=self.multitoken_current_token_var).pack(anchor=tk.W, pady=2)
        
        self.multitoken_message_count_var = tk.StringVar(value="0")
        ttk.Label(status_frame, text="Messages Sent:").pack(anchor=tk.W, pady=2)
        ttk.Label(status_frame, textvariable=self.multitoken_message_count_var).pack(anchor=tk.W, pady=2)
        
        # Load initial data
        self.load_multitoken_tokens()
        self.load_multitoken_wordlist()
    
    def setup_options_tab(self, parent):
        frame = ttk.Frame(parent, padding=10)
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Create a two-column layout
        left_column = ttk.Frame(frame)
        left_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        right_column = ttk.Frame(frame)
        right_column.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # General Options section
        ttk.Label(left_column, text="General Options", style='Header.TLabel').pack(anchor=tk.W, pady=5)
        
        # Manual Burst Mode Frame
        burst_frame = ttk.LabelFrame(left_column, text="Manual Burst Mode", padding=5)
        burst_frame.pack(fill=tk.X, pady=5, padx=5)
        
        # Enable/disable burst mode
        self.manual_burst_check = ttk.Checkbutton(
            burst_frame,
            text="Enable Manual Burst Mode",
            variable=self.manual_burst_var,
            command=self.toggle_manual_burst_mode
        )
        self.manual_burst_check.pack(anchor=tk.W, pady=2)
        
        # Manual delay setting
        manual_delay_frame = ttk.Frame(burst_frame)
        manual_delay_frame.pack(fill=tk.X, pady=2)
        
        self.manual_delay_label = ttk.Label(manual_delay_frame, text=f"Normal Delay: {self.manual_burst_delay:.2f}s")
        self.manual_delay_label.pack(anchor=tk.W, pady=1)
        
        self.manual_delay_slider = ttk.Scale(
            manual_delay_frame,
            from_=1.0, to=4.0,
            orient=tk.HORIZONTAL,
            value=self.manual_burst_delay,
            command=self.update_manual_delay,
            style='TScale'
        )
        self.manual_delay_slider.pack(fill=tk.X, pady=2)
        
        # Burst interval setting
        burst_interval_frame = ttk.Frame(burst_frame)
        burst_interval_frame.pack(fill=tk.X, pady=2)
        
        self.burst_interval_label = ttk.Label(burst_interval_frame, text=f"Burst Every: {self.manual_burst_interval:.0f}s")
        self.burst_interval_label.pack(anchor=tk.W, pady=1)
        
        self.burst_interval_slider = ttk.Scale(
            burst_interval_frame,
            from_=5, to=100,
            orient=tk.HORIZONTAL,
            value=self.manual_burst_interval,
            command=self.update_burst_interval,
            style='TScale'
        )
        self.burst_interval_slider.pack(fill=tk.X, pady=2)
        
        # Burst duration setting
        burst_duration_frame = ttk.Frame(burst_frame)
        burst_duration_frame.pack(fill=tk.X, pady=2)
        
        self.burst_duration_label = ttk.Label(burst_duration_frame, text=f"Burst Duration: {self.manual_burst_duration:.0f}s")
        self.burst_duration_label.pack(anchor=tk.W, pady=1)
        
        self.burst_duration_slider = ttk.Scale(
            burst_duration_frame,
            from_=1, to=30,
            orient=tk.HORIZONTAL,
            value=self.manual_burst_duration,
            command=self.update_burst_duration,
            style='TScale'
        )
        self.burst_duration_slider.pack(fill=tk.X, pady=2)
        
        # Add a description label
        ttk.Label(burst_frame, 
                 text="This mode sends messages with a customizable delay, but\nevery few seconds it bursts messages as fast as possible.",
                 wraplength=300).pack(anchor=tk.W, pady=3)
        
        # Right column - Message Collector
        ttk.Label(right_column, text="Message Collector", style='Header.TLabel').pack(anchor=tk.W, pady=5)
        
        # Message Collector Frame
        collector_frame = ttk.LabelFrame(right_column, text="Collects Your Mesasages", padding=5)
        collector_frame.pack(fill=tk.X, pady=5, padx=5)
        
        # Channel ID for message collection
        ttk.Label(collector_frame, text="Channel ID:").pack(anchor=tk.W, pady=2)
        self.collector_channel_entry = ttk.Entry(collector_frame, style='TEntry')
        self.collector_channel_entry.pack(fill=tk.X, pady=3)
        
        # Comma separation option
        self.collector_comma_var = tk.BooleanVar(value=self.message_collector_comma_separated)
        comma_check = ttk.Checkbutton(
            collector_frame,
            text="Separate messages with commas",
            variable=self.collector_comma_var,
            command=lambda: setattr(self, 'message_collector_comma_separated', self.collector_comma_var.get())
        )
        comma_check.pack(anchor=tk.W, pady=3)
        
        # Wordlist file name
        ttk.Label(collector_frame, text="Save to wordlist:").pack(anchor=tk.W, pady=2)
        self.collector_wordlist_entry = ttk.Entry(collector_frame, style='TEntry')
        self.collector_wordlist_entry.insert(0, self.mandem_wordlist_file)
        self.collector_wordlist_entry.pack(fill=tk.X, pady=3)
        
        # Start/Stop button
        self.collector_toggle_btn = ttk.Button(
            collector_frame,
            text="Start Collecting Messages",
            command=self.toggle_message_collector
        )
        self.collector_toggle_btn.pack(fill=tk.X, pady=5)
        
        # Status label
        self.collector_status_var = tk.StringVar(value="Status: Idle")
        ttk.Label(collector_frame, textvariable=self.collector_status_var).pack(anchor=tk.W, pady=3)
        
        # Description
        ttk.Label(collector_frame, 
                 text="This will collect messages sent by your account in the specified channel and add them to the wordlist.",
                 wraplength=300).pack(anchor=tk.W, pady=3)
        
    def setup_mandem_tab(self, parent):  # Keeping function name for compatibility
        # Main layout - split into left and right panes
        paned_window = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        left_pane = ttk.Frame(paned_window)
        right_pane = ttk.Frame(paned_window)
        paned_window.add(left_pane, weight=3)
        paned_window.add(right_pane, weight=1)
        
        # Left pane - channel ID and controls
        channel_frame = ttk.Frame(left_pane)
        channel_frame.pack(fill=tk.X, pady=3)
        ttk.Label(channel_frame, text="Autobeef Channel ID:", style='Section.TLabel').pack(anchor=tk.W)
        self.mandem_channel_entry = ttk.Entry(channel_frame, style='TEntry')
        self.mandem_channel_entry.pack(fill=tk.X, pady=3)
        
        # Target user frame
        target_frame = ttk.Frame(left_pane)
        target_frame.pack(fill=tk.X, pady=3)
        ttk.Label(target_frame, text="Target User ID (for replies/pings):", style='Section.TLabel').pack(anchor=tk.W)
        self.mandem_target_entry = ttk.Entry(target_frame, style='TEntry')
        self.mandem_target_entry.pack(fill=tk.X, pady=3)
        
        # Target everyone checkbox
        self.mandem_target_everyone_var = tk.BooleanVar(value=False)
        target_everyone_cb = ttk.Checkbutton(
            target_frame, 
            text="Target everyone (except self)", 
            variable=self.mandem_target_everyone_var,
            command=self.toggle_mandem_target_everyone
        )
        target_everyone_cb.pack(anchor=tk.W, pady=3)
        
        # Wordlist preview
        wordlist_frame = ttk.LabelFrame(left_pane, text="Wordlist Preview:")
        wordlist_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        wordlist_scroll = ttk.Scrollbar(wordlist_frame)
        wordlist_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.mandem_wordlist_preview = tk.Text(wordlist_frame, height=10, yscrollcommand=wordlist_scroll.set)
        self.mandem_wordlist_preview.pack(fill=tk.BOTH, expand=True)
        wordlist_scroll.config(command=self.mandem_wordlist_preview.yview)
        
        # Control buttons
        btn_frame = ttk.Frame(left_pane)
        btn_frame.pack(fill=tk.X, pady=8)
        
        self.mandem_toggle_btn = ttk.Button(
            btn_frame, 
            text="Start AutoBeefer", 
            command=self.toggle_mandem_mode
        )
        self.mandem_toggle_btn.pack(side=tk.LEFT, padx=3, fill=tk.X, expand=True)
        
        reload_btn = ttk.Button(
            btn_frame, 
            text="Reload Wordlist", 
            command=self.load_mandem_wordlist
        )
        reload_btn.pack(side=tk.LEFT, padx=3, fill=tk.X, expand=True)
        
        # Right pane - settings
        settings_frame = ttk.LabelFrame(right_pane, text="Options", padding=8)
        settings_frame.pack(fill=tk.X, pady=5, padx=5)
        
        # Reply chance slider
        ttk.Label(settings_frame, text=f"Reply Chance: {self.mandem_reply_chance}%").pack(anchor=tk.W, pady=2)
        self.reply_chance_slider = ttk.Scale(
            settings_frame,
            from_=0, to=100,
            orient=tk.HORIZONTAL,
            value=self.mandem_reply_chance,
            command=self.update_reply_chance
        )
        self.reply_chance_slider.pack(fill=tk.X, pady=3)
        
        # Ping chance slider
        ttk.Label(settings_frame, text=f"Ping Chance: {self.mandem_ping_chance}%").pack(anchor=tk.W, pady=2)
        self.ping_chance_slider = ttk.Scale(
            settings_frame,
            from_=0, to=100,
            orient=tk.HORIZONTAL,
            value=self.mandem_ping_chance,
            command=self.update_ping_chance
        )
        self.ping_chance_slider.pack(fill=tk.X, pady=3)
        
        # Repeat threshold slider
        ttk.Label(settings_frame, text=f"Repeat After: {int(self.mandem_repeat_threshold*100)}%").pack(anchor=tk.W, pady=2)
        self.repeat_threshold_slider = ttk.Scale(
            settings_frame,
            from_=10, to=100,
            orient=tk.HORIZONTAL,
            value=self.mandem_repeat_threshold*100,
            command=self.update_repeat_threshold
        )
        self.repeat_threshold_slider.pack(fill=tk.X, pady=3)
        
        # Reset threshold
        ttk.Label(settings_frame, text="Reset after messages:").pack(anchor=tk.W, pady=2)
        self.reset_threshold_var = tk.IntVar(value=self.mandem_reset_threshold)
        reset_threshold_entry = ttk.Entry(settings_frame, textvariable=self.reset_threshold_var)
        reset_threshold_entry.pack(fill=tk.X, pady=3)
        
        # Add a label to show message usage stats
        self.message_usage_var = tk.StringVar(value="Messages used: 0/0 (0%)")
        ttk.Label(settings_frame, textvariable=self.message_usage_var).pack(anchor=tk.W, pady=5)
        
        # Ping settings frame
        ping_frame = ttk.LabelFrame(right_pane, text="Ping Settings", padding=8)
        ping_frame.pack(fill=tk.X, pady=5, padx=5)
        
        # Ping chance slider
        ttk.Label(ping_frame, text=f"Ping Chance: {self.mandem_ping_chance}%").pack(anchor=tk.W, pady=2)
        self.ping_chance_slider = ttk.Scale(
            ping_frame,
            from_=0, to=100,
            orient=tk.HORIZONTAL,
            value=self.mandem_ping_chance,
            command=self.update_ping_chance
        )
        self.ping_chance_slider.pack(fill=tk.X, pady=3)
        
        # Ping position options
        ttk.Label(ping_frame, text="Ping Position:").pack(anchor=tk.W, pady=2)
        self.ping_position_var = tk.StringVar(value=self.mandem_ping_position)
        ping_pos_frame = ttk.Frame(ping_frame)
        ping_pos_frame.pack(fill=tk.X, pady=3)
        
        ttk.Radiobutton(
            ping_pos_frame, 
            text="Start", 
            variable=self.ping_position_var, 
            value="start"
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Radiobutton(
            ping_pos_frame, 
            text="End", 
            variable=self.ping_position_var, 
            value="end"
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Radiobutton(
            ping_pos_frame, 
            text="Random", 
            variable=self.ping_position_var, 
            value="random"
        ).pack(side=tk.LEFT, padx=5)
        
        # Ping after chance (for random position)
        ttk.Label(ping_frame, text=f"Chance to ping after message: {self.mandem_ping_after_chance}%").pack(anchor=tk.W, pady=2)
        self.ping_after_slider = ttk.Scale(
            ping_frame,
            from_=0, to=100,
            orient=tk.HORIZONTAL,
            value=self.mandem_ping_after_chance,
            command=self.update_ping_after_chance
        )
        self.ping_after_slider.pack(fill=tk.X, pady=3)
        
        # Load the wordlist initially
        self.load_mandem_wordlist()

    def toggle_mandem_target_everyone(self):
        if self.mandem_target_everyone_var.get():
            self.mandem_target_entry.config(state='disabled')
        else:
            self.mandem_target_entry.config(state='normal')
    
    def update_reply_chance(self, value):
        self.mandem_reply_chance = int(float(value))
        for child in self.reply_chance_slider.master.winfo_children():
            if isinstance(child, ttk.Label) and "Reply Chance" in child.cget("text"):
                child.config(text=f"Reply Chance: {self.mandem_reply_chance}%")
    
    def update_ping_chance(self, value):
        self.mandem_ping_chance = int(float(value))
        for child in self.ping_chance_slider.master.winfo_children():
            if isinstance(child, ttk.Label) and "Ping Chance" in child.cget("text"):
                child.config(text=f"Ping Chance: {self.mandem_ping_chance}%")
    
    def update_ping_after_chance(self, value):
        self.mandem_ping_after_chance = int(float(value))
        for child in self.ping_after_slider.master.winfo_children():
            if isinstance(child, ttk.Label) and "Chance to ping after" in child.cget("text"):
                child.config(text=f"Chance to ping after message: {self.mandem_ping_after_chance}%")
    
    def update_repeat_threshold(self, value):
        self.mandem_repeat_threshold = float(value) / 100
        for child in self.repeat_threshold_slider.master.winfo_children():
            if isinstance(child, ttk.Label) and "Repeat After" in child.cget("text"):
                child.config(text=f"Repeat After: {int(self.mandem_repeat_threshold*100)}%")
        
        # Update the message usage stats
        if hasattr(self, 'message_usage_var'):
            total = len(self.mandem_wordlist) if self.mandem_wordlist else 0
            used = len(self.mandem_used_messages) if hasattr(self, 'mandem_used_messages') else 0
            percent = (used / total * 100) if total > 0 else 0
            self.message_usage_var.set(f"Messages used: {used}/{total} ({percent:.1f}%)")
    
    def load_mandem_wordlist(self):
        self.mandem_wordlist = []
        try:
            if not os.path.exists(self.mandem_wordlist_file):
                with open(self.mandem_wordlist_file, 'w', encoding='utf-8') as f:
                    f.write("# Add your messages here, one per line.\n")
                    f.write("# Use commas to split messages in ladder mode.\n")
                    f.write("Hello there\n")
                    f.write("What's good?\n")
                    f.write("This is a test,of the ladder mode,it sends these separately\n")
                    f.write("Yo\n")
                    f.write("Mandem vibes\n")
                self.log(f"Created sample '{self.mandem_wordlist_file}' for Mandem mode.")
            
            with open(self.mandem_wordlist_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    self.mandem_wordlist.append(line)
            
            # Update preview
            self.mandem_wordlist_preview.config(state=tk.NORMAL)
            self.mandem_wordlist_preview.delete("1.0", tk.END)
            if not self.mandem_wordlist:
                self.mandem_wordlist_preview.insert(tk.END, "No messages found in wordlist.")
            else:
                for i, message in enumerate(self.mandem_wordlist[:20]):  # Show first 20 only
                    self.mandem_wordlist_preview.insert(tk.END, f"{i+1}. {message}\n")
                if len(self.mandem_wordlist) > 20:
                    self.mandem_wordlist_preview.insert(tk.END, f"... and {len(self.mandem_wordlist)-20} more messages")
            self.mandem_wordlist_preview.config(state=tk.DISABLED)
            
            self.log(f"✓ Loaded {len(self.mandem_wordlist)} messages from '{self.mandem_wordlist_file}'.")
        except Exception as e:
            self.log(f"✗ Error loading Mandem wordlist: {str(e)}", True)
    
    def toggle_mandem_mode(self):
        if self.mandem_mode_enabled:
            # Stop AutoBeefer mode
            self.mandem_mode_enabled = False
            self.mandem_thread_stop_event.set()
            self.mandem_toggle_btn.config(text="Start AutoBeefer")
            self.log("AutoBeefer mode stopped.")
            
            # Stop the stats update
            if hasattr(self, 'stats_update_id'):
                self.root.after_cancel(self.stats_update_id)
        else:
            # Start AutoBeefer mode
            if not self.validate_token():
                self.log("✗ Error: Invalid token format, cannot start AutoBeefer mode.", True)
                return
                
            channel_id = self.mandem_channel_entry.get().strip()
            if not channel_id:
                self.log("✗ Error: Missing channel ID, cannot start AutoBeefer mode.", True)
                return
                
            if not self.mandem_wordlist:
                self.log("✗ Error: Wordlist is empty, cannot start AutoBeefer mode.", True)
                return
            
            self.mandem_mode_enabled = True
            self.mandem_channel_id = channel_id
            self.mandem_target_id = self.mandem_target_entry.get().strip()
            self.mandem_target_everyone = self.mandem_target_everyone_var.get()
            self.mandem_reset_threshold = self.reset_threshold_var.get()
            self.mandem_ping_position = self.ping_position_var.get()
            self.mandem_thread_stop_event.clear()
            self.mandem_used_messages = []  # Reset used messages when starting
            self.mandem_last_pinged_id = None  # Reset last pinged user
            
            # Start the AutoBeefer thread
            if not hasattr(self, 'mandem_thread') or not self.mandem_thread or not self.mandem_thread.is_alive():
                self.mandem_thread = Thread(target=self.mandem_loop, daemon=True)
                self.mandem_thread.start()
            
            # Start periodic stats update
            self.update_message_usage_stats()
            
            self.mandem_toggle_btn.config(text="Stop AutoBeefer")
            self.log("AutoBeefer mode started.")
    
    def update_message_usage_stats(self):
        if hasattr(self, 'message_usage_var'):
            total = len(self.mandem_wordlist) if self.mandem_wordlist else 0
            used = len(self.mandem_used_messages) if hasattr(self, 'mandem_used_messages') else 0
            percent = (used / total * 100) if total > 0 else 0
            self.message_usage_var.set(f"Messages used: {used}/{total} ({percent:.1f}%)")
        
        # Schedule next update if Mandem mode is still active
        if self.mandem_mode_enabled:
            self.stats_update_id = self.root.after(1000, self.update_message_usage_stats)
    
    def mandem_loop(self):
        self.log("AutoBeefer thread started.")
        last_send_time = 0
        
        # Clear used messages list when starting
        self.mandem_used_messages = []
        
        while self.running and not self.mandem_thread_stop_event.is_set():
            try:
                if not self.mandem_mode_enabled:
                    break
                
                # Check if we need to wait due to rate limiting
                if self.rate_limited:
                    sleep_time = max(2,self.rate_limit_reset - time.time())
                    if sleep_time > 0:
                        time.sleep(sleep_time)
                    self.rate_limited = False
                
                # Calculate delay based on burst mode settings or base send delay
                current_time = time.time()
                
                # Check if manual burst mode is enabled - this takes priority over all other modes
                if self.manual_burst_enabled:
                    if self.in_burst_mode:
                        # Force zero delay during burst for immediate sending
                        delay = 0.0
                        # Force time_since_last_send to be large to bypass delay check
                        time_since_last_send = 999999.0
                    else:
                        delay = self.manual_burst_delay  # Use configured manual delay
                else:
                    # Use normal delay from slider
                    delay = self.base_send_delay.get()
                
                time_since_last_send = current_time - last_send_time
                
                if time_since_last_send < delay:
                    time.sleep(max(0.1, delay - time_since_last_send))
                    continue
                
                # Check if we need to reset due to message count
                if self.mandem_message_count >= self.mandem_reset_threshold:
                    self.log(f"AutoBeefer reset after {self.mandem_message_count} messages.")
                    self.mandem_message_count = 0
                    self.mandem_last_message_id = self.get_latest_message_id(self.mandem_channel_id)
                
                # Get a random message from the wordlist that hasn't been used recently
                if not self.mandem_wordlist:
                    time.sleep(1)
                    continue
                
                # Check if we need to reset the used messages list
                # If 90% of all messages have been used, reset the list
                if len(self.mandem_used_messages) >= len(self.mandem_wordlist) * self.mandem_repeat_threshold:
                    self.log(f"Reset message history after using {len(self.mandem_used_messages)} unique messages")
                    self.mandem_used_messages = []
                
                # Get available messages (those not recently used)
                available_messages = [msg for msg in self.mandem_wordlist if msg not in self.mandem_used_messages]
                
                # If all messages have been used (shouldn't happen with the reset above, but just in case)
                if not available_messages:
                    available_messages = self.mandem_wordlist
                    self.mandem_used_messages = []
                
                # Select a random message from available ones
                message = random.choice(available_messages)
                self.mandem_used_messages.append(message)
                
                # Check if we should reply to a message
                should_reply = random.random() < (self.mandem_reply_chance / 100)
                reply_to_id = None
                
                if should_reply:
                    # Get a message to reply to
                    if self.mandem_target_everyone:
                        reply_to_id = self.get_random_message_id(self.mandem_channel_id)
                    elif self.mandem_target_id:
                        reply_to_id = self.get_latest_user_message_id(self.mandem_channel_id, self.mandem_target_id)
                
                # Check if we should ping someone
                should_ping = random.random() < (self.mandem_ping_chance / 100)
                ping_text = ""
                
                if should_ping:
                    # Determine who to ping
                    ping_id = None
                    
                    if self.mandem_target_everyone:
                        # If targeting everyone, get a random user from recent messages
                        # but not the same as last time if possible
                        user_ids = self.get_recent_user_ids(self.mandem_channel_id)
                        if user_ids:
                            # Try to avoid pinging the same person twice in a row
                            if self.mandem_last_pinged_id in user_ids and len(user_ids) > 1:
                                user_ids.remove(self.mandem_last_pinged_id)
                            ping_id = random.choice(user_ids)
                            self.mandem_last_pinged_id = ping_id
                    elif self.mandem_target_id:
                        ping_id = self.mandem_target_id
                    
                    if ping_id:
                        ping_text = f"<@{ping_id}>"
                
                # Format the message with ping based on position setting
                formatted_message = self.format_message(message)
                
                if ping_text:
                    ping_position = self.ping_position_var.get()
                    
                    if ping_position == "start":
                        formatted_message = f"{ping_text} {formatted_message}"
                    elif ping_position == "end":
                        formatted_message = f"{formatted_message} {ping_text}"
                    elif ping_position == "random":
                        # Randomly decide whether to put ping before or after
                        if random.random() < (self.mandem_ping_after_chance / 100):
                            formatted_message = f"{formatted_message} {ping_text}"
                        else:
                            formatted_message = f"{ping_text} {formatted_message}"
                
                # Handle ladder mode (comma-separated messages)
                if "," in message and not self.space_split_enabled:
                    ladder_messages = message.split(",")
                    for i, ladder_msg in enumerate(ladder_messages):
                        if not ladder_msg.strip():
                            continue
                        
                        # Only apply ping to first message in ladder
                        if i == 0 and ping_text and ping_position == "start":
                            ladder_formatted = f"{ping_text} {self.format_message(ladder_msg.strip())}"
                        elif i == len(ladder_messages) - 1 and ping_text and ping_position == "end":
                            ladder_formatted = f"{self.format_message(ladder_msg.strip())} {ping_text}"
                        else:
                            ladder_formatted = self.format_message(ladder_msg.strip())
                        
                        # Use a flexible approach to handle any number of return values
                        result = self.send_message(
                            self.mandem_channel_id, 
                            ladder_formatted,
                            reply_to_id if i == 0 else None  # Only reply with the first message
                        )
                        
                        # Extract the values we need, regardless of how many are returned
                        success = result[0] if len(result) > 0 else False
                        error_type = result[1] if len(result) > 1 else None
                        msg_id = result[2] if len(result) > 2 else None
                        
                        if success:
                            last_send_time = time.time()
                            self.mandem_message_count += 1
                            # Store the message ID for potential future replies
                            if msg_id:
                                self.last_sent_message_id = msg_id
                        elif error_type == "rate_limit":
                            # Wait for rate limit to reset
                            sleep_time = max(2,self.rate_limit_reset - time.time())
                            if sleep_time > 0:
                                time.sleep(sleep_time)
                            self.rate_limited = False
                        
                        # Small delay between ladder messages
                        time.sleep(random.uniform(0.5, 1.5))
                else:
                    # Send a single message
                    # Use a flexible approach to handle any number of return values
                    result = self.send_message(
                        self.mandem_channel_id, 
                        formatted_message,
                        reply_to_id
                    )
                    
                    # Extract the values we need, regardless of how many are returned
                    success = result[0] if len(result) > 0 else False
                    error_type = result[1] if len(result) > 1 else None
                    msg_id = result[2] if len(result) > 2 else None
                    
                    if success:
                        last_send_time = time.time()
                        self.mandem_message_count += 1
                        # Store the message ID for potential future replies
                        if msg_id:
                            self.last_sent_message_id = msg_id
                    elif error_type == "permanent":
                        # If there's a permanent error, remove this message from the used list
                        # so we don't count it against our threshold
                        if message in self.mandem_used_messages:
                            self.mandem_used_messages.remove(message)
                
                # Random delay between messages to seem more human-like
                # Skip this delay if we're in burst mode
                if not (self.manual_burst_enabled and self.in_burst_mode):
                    time.sleep(random.uniform(delay, delay * 1.5))
                
            except Exception as e:
                self.log(f"✗ Error in AutoBeefer loop: {str(e)}", True)
                time.sleep(5)  # Wait a bit before retrying
        
        self.log("AutoBeefer thread stopped.")

    def get_reply_target(self):
        """Get the message ID to reply to, if any"""
        if hasattr(self, 'reply_to_message_id') and self.reply_to_message_id:
            reply_id = self.reply_to_message_id
            # Clear the reply target after using it
            self.reply_to_message_id = None
            self.update_reply_status(None)
            return reply_id
        return None

    def set_reply_target(self, message_id):
        """Set a message to reply to"""
        if message_id:
            self.reply_to_message_id = message_id
            self.update_reply_status(message_id)
            self.log(f"✓ Set to reply to message: {message_id[:10]}...")
            self.message_entry.focus_set()
        else:
            self.log("✗ Cannot set reply: Invalid message ID", True)

    def update_reply_status(self, message_id):
        """Update UI to show reply status"""
        if message_id:
            self.status_var.set(f"Replying to message: {message_id[:10]}...")
        else:
            self.status_var.set("Ready")
        self.update_status()

    def clear_reply_target(self):
        """Clear the current reply target"""
        self.reply_to_message_id = None
        self.update_reply_status(None)

    def set_reply_to_last_message(self):
        """Set reply target to the last message we sent"""
        if hasattr(self, 'last_sent_message_id') and self.last_sent_message_id:
            self.set_reply_target(self.last_sent_message_id)
        else:
            # Try to get the last message from the channel
            channel_id = self.channel_entry.get().strip()
            if channel_id:
                try:
                    response = requests.get(
                        f"https://discord.com/api/v9/channels/{channel_id}/messages?limit=10",
                        headers=self.get_headers(),
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        messages = response.json()
                        if messages and len(messages) > 0:
                            # Get the first message (most recent)
                            message_id = messages[0].get('id')
                            if message_id:
                                self.set_reply_target(message_id)
                                return
                except Exception as e:
                    self.log(f"✗ Error getting recent messages: {str(e)}", True)
            
            self.log("✗ No recent message to reply to", True)

    def get_latest_message_id(self, channel_id):
        try:
            response = requests.get(
                f"https://discord.com/api/v9/channels/{channel_id}/messages?limit=1",
                headers=self.get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                messages = response.json()
                if messages and len(messages) > 0:
                    return messages[0]['id']
            return None
        except Exception as e:
            self.log(f"✗ Error getting latest message: {str(e)}", True)
            return None
    
    def get_random_message_id(self, channel_id):
        """Get a random message ID from recent messages in a channel, excluding own messages"""
        try:
            response = requests.get(
                f"https://discord.com/api/v9/channels/{channel_id}/messages?limit=50",
                headers=self.get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                messages = response.json()
                if messages and len(messages) > 0:
                    # Filter out own messages
                    filtered_messages = [msg for msg in messages if not self.is_own_message(msg)]
                    if filtered_messages:
                        return random.choice(filtered_messages)['id']
                return None
        except Exception as e:
            self.log(f"✗ Error getting random message: {str(e)}", True)
            return None
    
    def get_latest_user_message_id(self, channel_id, user_id):
        """Get the latest message ID from a specific user in a channel"""
        try:
            response = requests.get(
                f"https://discord.com/api/v9/channels/{channel_id}/messages?limit=50",
                headers=self.get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                messages = response.json()
                for msg in messages:
                    if msg.get('author', {}).get('id') == user_id:
                        return msg.get('id')
            return None
        except Exception as e:
            self.log(f"✗ Error getting latest user message: {str(e)}", True)
            return None
    
    def get_recent_user_ids(self, channel_id):
        try:
            response = requests.get(
                f"https://discord.com/api/v9/channels/{channel_id}/messages?limit=50",
                headers=self.get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                messages = response.json()
                if messages and len(messages) > 0:
                    # Extract user IDs from recent messages
                    user_ids = []
                    for msg in messages:
                        author_id = msg.get('author', {}).get('id')
                        if author_id and author_id not in user_ids:
                            user_ids.append(author_id)
                    return user_ids
                return []
        except Exception as e:
            self.log(f"✗ Error getting recent user IDs: {str(e)}", True)
            return []
    
    def is_own_message(self, message):
        """Check if a message was sent by the current user"""
        try:
            # Get user ID from token
            if not hasattr(self, '_user_id'):
                self._user_id = self.get_user_id()
            
            return message.get('author', {}).get('id') == self._user_id
        except:
            return False
            
    def get_user_id(self):
        """Get the user ID from the token"""
        try:
            response = requests.get(
                "https://discord.com/api/v9/users/@me",
                headers=self.get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json().get('id')
            return None
        except Exception as e:
            self.log(f"✗ Error getting user ID: {str(e)}", True)
            return None
            
    # Multitoken mode methods
    def update_msgs_until_rotation(self, value):
        self.multitoken_msgs_until_rotation = int(float(value))
        self.msgs_until_rotation_label.config(text=f"Msgs until rotation: {self.multitoken_msgs_until_rotation}")
        
    def update_message_delay(self, value):
        self.multitoken_delay = int(float(value))
        self.message_delay_label.config(text=f"Message delay: {self.multitoken_delay}ms")
        
    def toggle_simultaneous_messages(self):
        """Toggle simultaneous message sending"""
        self.multitoken_simultaneous_enabled = self.simultaneous_var.get()
        self.log(f"Simultaneous messages {'enabled' if self.multitoken_simultaneous_enabled else 'disabled'}")
        
    def update_simultaneous_count(self, value):
        """Update the number of simultaneous messages"""
        self.multitoken_simultaneous_count = int(float(value))
        self.simultaneous_count_label.config(text=f"Simultaneous count: {self.multitoken_simultaneous_count}")
        
    def update_rate_limit_reset_time(self, value):
        """Update the rate limit reset time"""
        self.multitoken_rate_limit_reset_time = int(float(value))
        self.rate_limit_reset_label.config(text=f"Rate limit reset: {self.multitoken_rate_limit_reset_time}s")
        
    def update_spontaneous_chance(self, value):
        """Update the spontaneous message chance"""
        self.multitoken_spontaneous_chance = int(float(value))
        self.spontaneous_chance_label.config(text=f"Extra spontaneous msg chance: {self.multitoken_spontaneous_chance}%")
        
    def update_mps_counter(self, mps):
        """Update the messages per second counter"""
        self.multitoken_mps_var.set(f"{mps:.2f} msg/s")
        
    def load_multitoken_tokens(self):
        self.multitoken_tokens = []
        tokens_file = "tokens2.txt"
        
        try:
            if not os.path.exists(tokens_file):
                with open(tokens_file, 'w', encoding='utf-8') as f:
                    f.write("# Add your Discord tokens here, one per line.\n")
                    f.write("# Lines starting with # are ignored.\n")
                self.log(f"Created empty '{tokens_file}' for Multitoken mode.")
            
            with open(tokens_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    self.multitoken_tokens.append(line)
            
            # Update preview with masked tokens
            self.multitoken_preview.config(state=tk.NORMAL)
            self.multitoken_preview.delete("1.0", tk.END)
            
            if not self.multitoken_tokens:
                self.multitoken_preview.insert(tk.END, "No tokens found in tokens2.txt.")
            else:
                for i, token in enumerate(self.multitoken_tokens, 1):
                    # Mask the token for security
                    masked_token = token[:10] + "..." + token[-5:] if len(token) > 15 else token
                    self.multitoken_preview.insert(tk.END, f"{i}. {masked_token}\n")
            
            self.multitoken_preview.config(state=tk.DISABLED)
            self.log(f"Loaded {len(self.multitoken_tokens)} tokens for Multitoken mode.")
            
        except Exception as e:
            self.log(f"Error loading tokens: {str(e)}")
            messagebox.showerror("Error", f"Failed to load tokens: {str(e)}")
    
    def load_multitoken_wordlist(self):
        self.multitoken_wordlist = []
        try:
            if not os.path.exists(self.multitoken_wordlist_file):
                with open(self.multitoken_wordlist_file, 'w', encoding='utf-8') as f:
                    f.write("# Add your messages here, separated by commas.\n")
                    f.write("# Each comma creates a separate message.\n")
                    f.write("Hello there,What's up?,How are you doing?\n")
                    f.write("This is a test,of the multitoken mode,it sends these separately\n")
                    f.write("Yo,Hey,Hi,Hello,Greetings\n")
                self.log(f"Created sample '{self.multitoken_wordlist_file}' for Multitoken mode.")
            
            # Read the entire file content
            with open(self.multitoken_wordlist_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Process the content
            lines = content.split('\n')
            processed_content = ""
            
            # Skip comments and combine non-comment lines
            for line in lines:
                if line.strip() and not line.strip().startswith('#'):
                    processed_content += line + "\n"
            
            # Split by commas but preserve newlines within messages
            if processed_content:
                # Replace newlines with a special marker temporarily
                temp_content = processed_content.replace('\n', '§NEWLINE§')
                
                # Split by commas
                raw_messages = temp_content.split(',')
                
                # Process each message
                for msg in raw_messages:
                    # Restore newlines and strip only leading/trailing whitespace
                    cleaned_msg = msg.replace('§NEWLINE§', '\n').strip()
                    if cleaned_msg:
                        self.multitoken_wordlist.append(cleaned_msg)
            
            # Update preview
            self.multitoken_wordlist_preview.config(state=tk.NORMAL)
            self.multitoken_wordlist_preview.delete("1.0", tk.END)
            
            if not self.multitoken_wordlist:
                self.multitoken_wordlist_preview.insert(tk.END, "No messages found in wordlist.")
            else:
                for i, message in enumerate(self.multitoken_wordlist, 1):
                    self.multitoken_wordlist_preview.insert(tk.END, f"{i}. {message}\n")
            
            self.multitoken_wordlist_preview.config(state=tk.DISABLED)
            self.log(f"Loaded {len(self.multitoken_wordlist)} messages for Multitoken mode.")
            
        except Exception as e:
            self.log(f"Error loading multitoken wordlist: {str(e)}")
            messagebox.showerror("Error", f"Failed to load multitoken wordlist: {str(e)}")
    
    def toggle_multitoken_mode(self):
        if self.multitoken_active:
            # Stop multitoken mode
            self.stop_multitoken_mode()
        else:
            # Start multitoken mode
            self.start_multitoken_mode()
    
    def start_multitoken_mode(self):
        # Check if we have tokens and a channel ID
        if not self.multitoken_tokens:
            messagebox.showerror("Error", "No tokens loaded. Please add tokens to tokens2.txt and reload.")
            return
            
        channel_id = self.multitoken_channel_entry.get().strip()
        if not channel_id:
            messagebox.showerror("Error", "Please enter a channel ID.")
            return
            
        if not self.multitoken_wordlist:
            messagebox.showerror("Error", "No messages loaded. Please add messages to tokenwordlist.txt and reload.")
            return
        
        # Reset state
        self.multitoken_active = True
        self.multitoken_channel_id = channel_id
        self.multitoken_current_token_index = 0
        self.multitoken_message_count = 0
        self.multitoken_used_messages = []
        self.multitoken_rate_limited_tokens = {}  # Changed to dict to store timestamps
        self.multitoken_thread_stop_event.clear()
        
        # Update UI
        self.multitoken_toggle_btn.config(text="Stop Multitoken Mode")
        self.multitoken_current_token_var.set(f"Token #{self.multitoken_current_token_index + 1}")
        self.multitoken_message_count_var.set("0")
        self.multitoken_mps_var.set("0.00 msg/s")
        
        # Log spontaneous message settings
        self.log(f"Multitoken mode started with {self.multitoken_spontaneous_chance}% spontaneous message chance")
        
        # Start the multitoken thread
        self.multitoken_thread = Thread(target=self.multitoken_worker)
        self.multitoken_thread.daemon = True
        self.multitoken_thread.start()
        
        self.log("Multitoken mode started.")
    
    def stop_multitoken_mode(self):
        if not self.multitoken_active:
            return
            
        self.multitoken_active = False
        self.multitoken_thread_stop_event.set()
        
        # Update UI
        self.multitoken_toggle_btn.config(text="Start Multitoken Mode")
        self.multitoken_mps_var.set("0.00 msg/s")
        
        self.log("Multitoken mode stopped.")
    
    def multitoken_worker(self):
        """Worker thread for multitoken mode"""
        messages_sent_with_current_token = 0
        last_regular_message_time = time.time()
        last_spontaneous_check_time = time.time()
        last_mps_update_time = time.time()
        message_count_for_mps = 0
        
        while not self.multitoken_thread_stop_event.is_set():
            try:
                # Check for tokens that can be un-rate-limited
                self.check_rate_limited_tokens()
                
                current_time = time.time()
                
                # PART 1: Regular message sending (always happens)
                # Send a regular message immediately (no delay since we set it to 0)
                if self.send_regular_multitoken_message(messages_sent_with_current_token):
                    messages_sent_with_current_token += 1
                    message_count_for_mps += 1
                
                # Check if we need to rotate tokens
                if messages_sent_with_current_token >= self.multitoken_msgs_until_rotation:
                    self.rotate_token()
                    messages_sent_with_current_token = 0
                
                # PART 2: Spontaneous message sending (chance-based)
                # Only check for spontaneous messages every 2-5 seconds to prevent spam
                if current_time - last_spontaneous_check_time >= random.uniform(2, 5):
                    # Roll for spontaneous message chance
                    if random.randint(1, 100) <= self.multitoken_spontaneous_chance:
                        self.log(f"Spontaneous message triggered ({self.multitoken_spontaneous_chance}% chance)")
                        
                        # Check if simultaneous mode is enabled for spontaneous messages
                        if self.multitoken_simultaneous_enabled:
                            # Send multiple messages simultaneously
                            sent_count = self.send_simultaneous_messages(messages_sent_with_current_token, is_spontaneous=True)
                            message_count_for_mps += sent_count
                            # Don't count these toward token rotation since they're spontaneous
                        else:
                            # Send a single spontaneous message
                            if self.send_spontaneous_message():
                                message_count_for_mps += 1
                    
                    # Reset the spontaneous check timer regardless of whether we sent a message
                    last_spontaneous_check_time = current_time
                
                # Update messages per second counter every second
                if current_time - last_mps_update_time >= 1.0:
                    elapsed = current_time - last_mps_update_time
                    mps = message_count_for_mps / elapsed
                    self.root.after(0, lambda: self.update_mps_counter(mps))
                    message_count_for_mps = 0
                    last_mps_update_time = current_time
                
                # Small sleep to prevent tight loop
                time.sleep(0.1)
                
            except Exception as e:
                self.log(f"Error in multitoken worker: {str(e)}")
                time.sleep(1)  # Prevent tight loop on error
                
    def send_regular_multitoken_message(self, messages_sent_with_current_token):
        """Send a regular message with the current token"""
        # Get current token
        if self.multitoken_current_token_index >= len(self.multitoken_tokens):
            self.multitoken_current_token_index = 0
        
        current_token = self.multitoken_tokens[self.multitoken_current_token_index]
        
        # Skip rate-limited tokens
        if current_token in self.multitoken_rate_limited_tokens.keys():
            self.log(f"Token #{self.multitoken_current_token_index + 1} is rate-limited, rotating...")
            self.rotate_token()
            return False
        
        # Select a message
        message = self.get_next_multitoken_message()
        
        # Apply formatting with multitoken flag
        formatted_message = self.format_message(message, is_multitoken=True)
        
        # Send the message
        success = self.send_multitoken_message(current_token, formatted_message)
        
        if success:
            self.multitoken_message_count += 1
            self.root.after(0, lambda: self.multitoken_message_count_var.set(str(self.multitoken_message_count)))
            return True
        else:
            # If sending failed, mark token as rate-limited with current timestamp and rotate
            self.multitoken_rate_limited_tokens[current_token] = time.time()
            self.log(f"Token #{self.multitoken_current_token_index + 1} failed to send, marked as rate-limited for {self.multitoken_rate_limit_reset_time}s, rotating...")
            self.rotate_token()
            return False
            
    def send_spontaneous_message(self):
        """Send a single spontaneous message with a random token"""
        # Get available tokens (not rate-limited)
        available_tokens = [token for token in self.multitoken_tokens if token not in self.multitoken_rate_limited_tokens.keys()]
        
        if not available_tokens:
            self.log("✗ No available tokens for spontaneous message. All tokens are rate-limited.", True)
            return False
        
        # Select a random token from available ones
        token = random.choice(available_tokens)
        
        # Select a message
        message = self.get_next_multitoken_message()
        
        # Apply formatting with multitoken flag
        formatted_message = self.format_message(message, is_multitoken=True)
        
        # Add a special tag to indicate this is a spontaneous message
        formatted_message = f"{formatted_message}"
        
        # Send the message
        success = self.send_multitoken_message(token, formatted_message)
        
        if success:
            self.multitoken_message_count += 1
            self.root.after(0, lambda: self.multitoken_message_count_var.set(str(self.multitoken_message_count)))
            self.log(f"Sent spontaneous message successfully")
            return True
        else:
            # If sending failed, mark token as rate-limited
            self.multitoken_rate_limited_tokens[token] = time.time()
            self.log(f"Failed to send spontaneous message, token marked as rate-limited")
            return False
    
    def rotate_token(self):
        """Rotate to the next token"""
        self.multitoken_current_token_index = (self.multitoken_current_token_index + 1) % len(self.multitoken_tokens)
        self.root.after(0, lambda: self.multitoken_current_token_var.set(f"Token #{self.multitoken_current_token_index + 1}"))
        self.log(f"Rotated to token #{self.multitoken_current_token_index + 1}")
    
    def send_simultaneous_messages(self, messages_sent_with_current_token, is_spontaneous=False):
        """Send multiple messages simultaneously"""
        # Get available tokens (not rate-limited)
        available_tokens = [token for token in self.multitoken_tokens if token not in self.multitoken_rate_limited_tokens.keys()]
        
        if not available_tokens:
            self.log("✗ No available tokens for simultaneous sending. All tokens are rate-limited.", True)
            time.sleep(1)  # Prevent tight loop
            return
        
        # Determine how many messages to send (limited by available tokens)
        count = min(self.multitoken_simultaneous_count, len(available_tokens))
        
        # Prepare messages and tokens
        messages = []
        tokens = []
        
        for _ in range(count):
            # Get a message
            message = self.get_next_multitoken_message()
            formatted_message = self.format_message(message, is_multitoken=True)
            messages.append(formatted_message)
            
            # Get a token (cycling through available ones)
            if self.multitoken_current_token_index >= len(self.multitoken_tokens):
                self.multitoken_current_token_index = 0
            
            current_token = self.multitoken_tokens[self.multitoken_current_token_index]
            
            # Skip rate-limited tokens
            while current_token in self.multitoken_rate_limited_tokens.keys():
                self.rotate_token()
                current_token = self.multitoken_tokens[self.multitoken_current_token_index]
            
            tokens.append(current_token)
            self.rotate_token()  # Move to next token for next message
        
        # Create and start threads for simultaneous sending
        threads = []
        results = [False] * count  # Track success/failure
        
        for i in range(count):
            thread = Thread(
                target=self._send_message_thread, 
                args=(tokens[i], messages[i], i, results)
            )
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Process results
        for i, success in enumerate(results):
            if success:
                self.multitoken_message_count += 1
                self.root.after(0, lambda: self.multitoken_message_count_var.set(str(self.multitoken_message_count)))
            else:
                # Mark token as rate-limited with timestamp
                self.multitoken_rate_limited_tokens[tokens[i]] = time.time()
                self.log(f"Token failed to send in simultaneous mode, marked as rate-limited for {self.multitoken_rate_limit_reset_time}s")
        
        # Log summary
        successful = sum(results)
        message_type = "spontaneous" if is_spontaneous else "regular"
        self.log(f"Sent {successful}/{count} simultaneous {message_type} messages")
        
        # Add a small delay after simultaneous sending
        time.sleep(self.multitoken_delay / 1000.0)
        
        # Return the number of successful messages
        return successful
    
    def _send_message_thread(self, token, message, index, results):
        """Thread function to send a message with a token"""
        success = self.send_multitoken_message(token, message)
        results[index] = success
    
    def get_next_multitoken_message(self):
        """Get the next message to send, avoiding repetition"""
        available_messages = [msg for msg in self.multitoken_wordlist if msg not in self.multitoken_used_messages]
        
        # If we've used 90% of messages, reset the used list
        if len(available_messages) <= len(self.multitoken_wordlist) * (1 - self.multitoken_repeat_threshold):
            self.multitoken_used_messages = []
            available_messages = self.multitoken_wordlist
        
        message = random.choice(available_messages)
        self.multitoken_used_messages.append(message)
        return message
    
    def send_multitoken_message(self, token, message):
        """Send a message using the specified token"""
        headers = {
            "Authorization": token if token.startswith("Bot ") else token,
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        
        payload = {"content": message}
        
        try:
            response = requests.post(
                f"https://discord.com/api/v9/channels/{self.multitoken_channel_id}/messages",
                headers=headers,
                json=payload
            )
            
            if response.status_code == 200 or response.status_code == 201:
                self.log(f"Message sent with token #{self.multitoken_current_token_index + 1}: {message[:30]}...")
                return True
            elif response.status_code == 429:
                # Rate limited
                retry_after = response.json().get("retry_after", 5)
                self.log(f"Rate limited on token #{self.multitoken_current_token_index + 1}. Retry after {retry_after}s")
                return False
            else:
                self.log(f"Failed to send message with token #{self.multitoken_current_token_index + 1}. Status: {response.status_code}")
                return False
                
        except Exception as e:
            self.log(f"Error sending message with token #{self.multitoken_current_token_index + 1}: {str(e)}")
            return False

if __name__ == "__main__":
    root = tk.Tk()
    print("Starting application...")
    app = DiscordMessageSender(root)
    print("Application initialized, entering mainloop...")
    root.mainloop()
    print("Application closed.")
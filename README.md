# NEXUS MESSENGER PRO

A Discord messaging tool with advanced features, now with a modern PyQt interface.

## Features

- Send messages to Discord channels
- Reply to messages
- Change group chat names
- Smart bold formatting
- Auto-corrections
- Mandem mode for automated messaging
- Typing indicators
- Message queue system
- Rate limit handling
- Prefix/suffix support
- Space split mode

## How to Use

1. Run `start_qt.cmd` or `python femboycord_qt.py` to start the application
2. Enter your Discord token
3. Enter the channel ID where you want to send messages
4. Type your message and press Enter to send

## Hotkeys

- **Enter**: Send message (unless space split mode is on)
- **Shift+Enter**: Add new line
- **F1**: Toggle prefix/suffix
- **F2**: Toggle space split
- **F3**: Toggle GC name changer mode
- **F4**: Toggle queue pause
- **F5**: Get last message ID for reply
- **Escape**: Clear reply target

## Modes

### Manual Mode
Send messages manually to Discord channels.

### Mandem Mode
Automated messaging with customizable settings:
- Reply chance
- Ping chance
- Ping after chance
- Repeat threshold
- Custom wordlist

## Requirements

- Python 3.8+
- PyQt5
- Requests

## Installation

```
pip install PyQt5 requests
```

Then run the application using:

```
python femboycord_qt.py
```

Or double-click on `start_qt.cmd` to run it directly.

## Notes

- Your Discord token is sensitive information. Never share it with anyone.
- Use this tool responsibly and in accordance with Discord's Terms of Service.
- The application does not store your token permanently.
import tkinter as tk
from tkinter import ttk

def main():
    root = tk.Tk()
    root.title("Tab Test")
    root.geometry("800x600")
    
    notebook = ttk.Notebook(root)
    notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    tab1 = ttk.Frame(notebook)
    notebook.add(tab1, text="Tab 1")
    
    tab2 = ttk.Frame(notebook)
    notebook.add(tab2, text="Tab 2")
    
    tab3 = ttk.Frame(notebook)
    notebook.add(tab3, text="Tab 3")
    
    tab4 = ttk.Frame(notebook)
    notebook.add(tab4, text="Tab 4")
    
    ttk.Label(tab1, text="This is Tab 1").pack(pady=20)
    ttk.Label(tab2, text="This is Tab 2").pack(pady=20)
    ttk.Label(tab3, text="This is Tab 3").pack(pady=20)
    ttk.Label(tab4, text="This is Tab 4").pack(pady=20)
    
    root.mainloop()

if __name__ == "__main__":
    main()
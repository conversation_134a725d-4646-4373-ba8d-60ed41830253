import os
import requests
import time
import colorama
from colorama import Fore, Style

# Initialize colorama
colorama.init()

def clear_screen():
    """Clear the console screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """Print the token checker header."""
    print(f"{Fore.MAGENTA}╔══════════════════════════════════════════════════════════╗{Style.RESET_ALL}")
    print(f"{Fore.MAGENTA}║{Fore.CYAN}                   TOKEN CHECKER                      {Fore.MAGENTA}║{Style.RESET_ALL}")
    print(f"{Fore.MAGENTA}╚══════════════════════════════════════════════════════════╝{Style.RESET_ALL}")

def load_tokens(file_path):
    """Load tokens from a file and automatically remove duplicates."""
    try:
        if not os.path.exists(file_path):
            print(f"{Fore.RED}Error: {file_path} does not exist.{Style.RESET_ALL}")
            return []
        
        with open(file_path, 'r') as file:
            tokens = [line.strip() for line in file.readlines() if line.strip()]
        
        # Remove duplicates while preserving order
        unique_tokens = []
        seen = set()
        duplicates_count = 0
        
        for token in tokens:
            if token not in seen:
                seen.add(token)
                unique_tokens.append(token)
            else:
                duplicates_count += 1
        
        # If duplicates were found, save the file with unique tokens
        if duplicates_count > 0:
            with open(file_path, 'w') as file:
                for token in unique_tokens:
                    file.write(f"{token}\n")
            print(f"{Fore.YELLOW}Automatically removed {duplicates_count} duplicate tokens{Style.RESET_ALL}")
        
        print(f"{Fore.GREEN}Loaded {len(unique_tokens)} unique tokens from {file_path}{Style.RESET_ALL}")
        return unique_tokens
    except Exception as e:
        print(f"{Fore.RED}Error loading tokens: {str(e)}{Style.RESET_ALL}")
        return []

def save_tokens(file_path, tokens):
    """Save tokens to a file."""
    try:
        with open(file_path, 'w') as file:
            for token in tokens:
                file.write(f"{token}\n")
        print(f"{Fore.GREEN}Successfully saved {len(tokens)} tokens to {file_path}{Style.RESET_ALL}")
        return True
    except Exception as e:
        print(f"{Fore.RED}Error saving tokens: {str(e)}{Style.RESET_ALL}")
        return False

def check_token(token):
    """Check if a token is valid."""
    headers = {
        "Authorization": token,
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get("https://discord.com/api/v9/users/@me", headers=headers)
        
        if response.status_code == 200:
            user_data = response.json()
            username = user_data.get("username", "Unknown")
            discriminator = user_data.get("discriminator", "0000")
            user_id = user_data.get("id", "Unknown")
            
            if discriminator == "0":
                full_username = username
            else:
                full_username = f"{username}#{discriminator}"
                
            return {
                "valid": True,
                "locked": False,
                "username": full_username,
                "id": user_id
            }
        elif response.status_code == 401:
            return {
                "valid": False,
                "locked": False,
                "error": "Invalid token"
            }
        elif response.status_code == 403:
            return {
                "valid": True,
                "locked": True,
                "error": "Token is locked or requires verification"
            }
        else:
            return {
                "valid": False,
                "locked": False,
                "error": f"Unknown error (Status code: {response.status_code})"
            }
    except Exception as e:
        return {
            "valid": False,
            "locked": False,
            "error": f"Request error: {str(e)}"
        }

def check_token_in_server(token, server_id):
    """Check if a token is in a specific server."""
    if not token.startswith("Bot ") and not token.startswith("Bearer "):
        auth_token = token
    else:
        auth_token = token
    
    headers = {
        "Authorization": auth_token,
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    
    try:
        guilds_response = requests.get("https://discord.com/api/v9/users/@me/guilds", headers=headers)
        if guilds_response.status_code == 200:
            guilds = guilds_response.json()
            for guild in guilds:
                if guild.get("id") == server_id:
                    return True
    except:
        pass
    
    try:
        guild_response = requests.get(f"https://discord.com/api/v9/guilds/{server_id}", headers=headers)
        if guild_response.status_code == 200:
            return True
    except:
        pass
    
    try:
        channels_response = requests.get(f"https://discord.com/api/v9/guilds/{server_id}/channels", headers=headers)
        if channels_response.status_code == 200:
            return True
    except:
        pass
    
    try:
        members_response = requests.get(
            f"https://discord.com/api/v9/guilds/{server_id}/members?limit=1", 
            headers=headers
        )
        if members_response.status_code == 200:
            return True
    except:
        pass
    
    try:
        member_response = requests.get(
            f"https://discord.com/api/v9/guilds/{server_id}/members/@me", 
            headers=headers
        )
        if member_response.status_code == 200:
            return True
    except:
        pass
    
    return False

def check_all_tokens(tokens, check_server=False, server_id=None):
    """Check all tokens and return results."""
    valid_tokens = []
    invalid_tokens = []
    locked_tokens = []
    not_in_server_tokens = []
    
    total = len(tokens)
    
    print(f"{Fore.CYAN}Checking {total} tokens...{Style.RESET_ALL}")
    print()
    
    for i, token in enumerate(tokens):
        print(f"{Fore.YELLOW}Token {i+1}/{total}: ", end="")
        
        if not token.startswith("Bot ") and not token.startswith("Bearer "):
            formatted_token = token
        else:
            formatted_token = token.split(" ", 1)[1] if " " in token else token
        
        result = check_token(formatted_token)
        
        if result["valid"]:
            if result["locked"]:
                print(f"{Fore.RED}LOCKED{Style.RESET_ALL}")
                locked_tokens.append(token)
            else:
                if check_server and server_id:
                    in_server = check_token_in_server(formatted_token, server_id)
                    
                    if in_server:
                        print(f"{Fore.GREEN}VALID{Style.RESET_ALL}")
                        valid_tokens.append(token)
                    else:
                        print(f"{Fore.YELLOW}NOT IN SERVER{Style.RESET_ALL}")
                        not_in_server_tokens.append(token)
                else:
                    print(f"{Fore.GREEN}VALID{Style.RESET_ALL}")
                    valid_tokens.append(token)
        else:
            print(f"{Fore.RED}INVALID{Style.RESET_ALL}")
            invalid_tokens.append(token)
        
        time.sleep(0.5)
    
    return {
        "valid": valid_tokens,
        "invalid": invalid_tokens,
        "locked": locked_tokens,
        "not_in_server": not_in_server_tokens
    }

def display_results(results, check_server=False):
    """Display the results of token checking."""
    print("\n" + "="*50)
    print(f"{Fore.CYAN}RESULTS:{Style.RESET_ALL}")
    print("="*50)
    
    print(f"{Fore.GREEN}Valid tokens: {len(results['valid'])}{Style.RESET_ALL}")
    print(f"{Fore.RED}Invalid tokens: {len(results['invalid'])}{Style.RESET_ALL}")
    print(f"{Fore.RED}Locked tokens: {len(results['locked'])}{Style.RESET_ALL}")
    
    if check_server:
        print(f"{Fore.YELLOW}Valid but not in server: {len(results['not_in_server'])}{Style.RESET_ALL}")
    
    print("="*50)

def main():
    """Main function."""
    tokens_file = "tokens2.txt"
    
    while True:
        clear_screen()
        print_header()
        
        print(f"\n{Fore.CYAN}Options:{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}1. {Fore.WHITE}Server Check (Check tokens in a specific server){Style.RESET_ALL}")
        print(f"{Fore.YELLOW}2. {Fore.WHITE}Basic Check (Check if tokens are valid/locked){Style.RESET_ALL}")
        print(f"{Fore.YELLOW}3. {Fore.WHITE}Remove Duplicate Tokens{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}4. {Fore.WHITE}Exit{Style.RESET_ALL}")
        
        choice = input(f"\n{Fore.MAGENTA}Enter your choice (1-4): {Style.RESET_ALL}")
        
        if choice == "1":
            clear_screen()
            print_header()
            
            # Check if tokens2.txt exists, if not, create it
            if not os.path.exists(tokens_file):
                print(f"{Fore.YELLOW}Warning: {tokens_file} does not exist. Creating an empty file.{Style.RESET_ALL}")
                with open(tokens_file, 'w') as f:
                    pass
                print(f"{Fore.GREEN}Created {tokens_file}. Please add your tokens to this file and run the program again.{Style.RESET_ALL}")
                input(f"\n{Fore.YELLOW}Press Enter to continue...{Style.RESET_ALL}")
                continue
            
            # Automatically remove duplicates first
            with open(tokens_file, 'r') as file:
                tokens = [line.strip() for line in file.readlines() if line.strip()]
            
            # Remove duplicates while preserving order
            unique_tokens = []
            seen = set()
            duplicates_count = 0
            
            for token in tokens:
                if token not in seen:
                    seen.add(token)
                    unique_tokens.append(token)
                else:
                    duplicates_count += 1
            
            # If duplicates were found, save the file with unique tokens
            if duplicates_count > 0:
                with open(tokens_file, 'w') as file:
                    for token in unique_tokens:
                        file.write(f"{token}\n")
                print(f"{Fore.YELLOW}Automatically removed {duplicates_count} duplicate tokens{Style.RESET_ALL}")
                tokens = unique_tokens
            
            server_id = input(f"\n{Fore.CYAN}Enter server ID to check: {Style.RESET_ALL}")
            if not server_id.strip():
                print(f"{Fore.RED}Error: Server ID cannot be empty.{Style.RESET_ALL}")
                input(f"\n{Fore.YELLOW}Press Enter to continue...{Style.RESET_ALL}")
                continue
            
            if not tokens:
                print(f"{Fore.RED}No tokens found in {tokens_file}. Please add tokens to this file.{Style.RESET_ALL}")
                input(f"\n{Fore.YELLOW}Press Enter to continue...{Style.RESET_ALL}")
                continue
            
            results = check_all_tokens(tokens, check_server=True, server_id=server_id)
            display_results(results, check_server=True)
            
            # Options for removing tokens
            print(f"\n{Fore.CYAN}What would you like to do?{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}1. {Fore.WHITE}Remove invalid and locked tokens{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}2. {Fore.WHITE}Remove tokens not in the server{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}3. {Fore.WHITE}Remove both invalid/locked and not in server tokens{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}4. {Fore.WHITE}Keep all tokens{Style.RESET_ALL}")
            
            remove_choice = input(f"\n{Fore.MAGENTA}Enter your choice (1-4): {Style.RESET_ALL}")
            
            if remove_choice == "1":
                # Remove invalid and locked tokens
                new_tokens = [t for t in tokens if t not in results['invalid'] and t not in results['locked']]
                save_tokens(tokens_file, new_tokens)
            elif remove_choice == "2":
                # Remove tokens not in server
                new_tokens = [t for t in tokens if t not in results['not_in_server']]
                save_tokens(tokens_file, new_tokens)
            elif remove_choice == "3":
                # Remove both invalid/locked and not in server tokens
                new_tokens = results['valid']  # Only keep valid tokens that are in the server
                save_tokens(tokens_file, new_tokens)
            
            input(f"\n{Fore.YELLOW}Press Enter to continue...{Style.RESET_ALL}")
            
        elif choice == "2":
            clear_screen()
            print_header()
            
            # Check if tokens2.txt exists, if not, create it
            if not os.path.exists(tokens_file):
                print(f"{Fore.YELLOW}Warning: {tokens_file} does not exist. Creating an empty file.{Style.RESET_ALL}")
                with open(tokens_file, 'w') as f:
                    pass
                print(f"{Fore.GREEN}Created {tokens_file}. Please add your tokens to this file and run the program again.{Style.RESET_ALL}")
                input(f"\n{Fore.YELLOW}Press Enter to continue...{Style.RESET_ALL}")
                continue
            
            # Automatically remove duplicates first
            with open(tokens_file, 'r') as file:
                tokens = [line.strip() for line in file.readlines() if line.strip()]
            
            # Remove duplicates while preserving order
            unique_tokens = []
            seen = set()
            duplicates_count = 0
            
            for token in tokens:
                if token not in seen:
                    seen.add(token)
                    unique_tokens.append(token)
                else:
                    duplicates_count += 1
            
            # If duplicates were found, save the file with unique tokens
            if duplicates_count > 0:
                with open(tokens_file, 'w') as file:
                    for token in unique_tokens:
                        file.write(f"{token}\n")
                print(f"{Fore.YELLOW}Automatically removed {duplicates_count} duplicate tokens{Style.RESET_ALL}")
                tokens = unique_tokens
            
            if not tokens:
                print(f"{Fore.RED}No tokens found in {tokens_file}. Please add tokens to this file.{Style.RESET_ALL}")
                input(f"\n{Fore.YELLOW}Press Enter to continue...{Style.RESET_ALL}")
                continue
            
            results = check_all_tokens(tokens)
            display_results(results)
            
            remove_option = input(f"\n{Fore.CYAN}Do you want to remove invalid/locked tokens? (y/n): {Style.RESET_ALL}").lower()
            if remove_option == 'y':
                new_tokens = results['valid']
                save_tokens(tokens_file, new_tokens)
            
            input(f"\n{Fore.YELLOW}Press Enter to continue...{Style.RESET_ALL}")
            
        elif choice == "3":
            clear_screen()
            print_header()
            
            # Check if tokens2.txt exists, if not, create it
            if not os.path.exists(tokens_file):
                print(f"{Fore.YELLOW}Warning: {tokens_file} does not exist. Creating an empty file.{Style.RESET_ALL}")
                with open(tokens_file, 'w') as f:
                    pass
                print(f"{Fore.GREEN}Created {tokens_file}. Please add your tokens to this file and run the program again.{Style.RESET_ALL}")
                input(f"\n{Fore.YELLOW}Press Enter to continue...{Style.RESET_ALL}")
                continue
            
            # Load tokens and check for duplicates
            print(f"\n{Fore.CYAN}Checking for duplicate tokens...{Style.RESET_ALL}")
            
            with open(tokens_file, 'r') as file:
                tokens = [line.strip() for line in file.readlines() if line.strip()]
            
            # Find duplicates while preserving order
            unique_tokens = []
            seen = set()
            duplicates = []
            
            for token in tokens:
                if token not in seen:
                    seen.add(token)
                    unique_tokens.append(token)
                else:
                    duplicates.append(token)
            
            if duplicates:
                print(f"\n{Fore.YELLOW}Found {len(duplicates)} duplicate tokens.{Style.RESET_ALL}")
                
                # Show a few examples of duplicates (masked for security)
                if len(duplicates) > 0:
                    print(f"\n{Fore.CYAN}Examples of duplicates:{Style.RESET_ALL}")
                    for i, dup in enumerate(duplicates[:5], 1):  # Show only first 5 duplicates
                        masked_token = dup[:10] + "..." + dup[-5:]
                        print(f"{i}. {masked_token}")
                    
                    if len(duplicates) > 5:
                        print(f"...and {len(duplicates) - 5} more")
                
                remove_option = input(f"\n{Fore.CYAN}Do you want to remove these duplicates? (y/n): {Style.RESET_ALL}").lower()
                if remove_option == 'y':
                    save_tokens(tokens_file, unique_tokens)
                    print(f"{Fore.GREEN}Successfully removed {len(duplicates)} duplicate tokens.{Style.RESET_ALL}")
                else:
                    print(f"{Fore.YELLOW}No changes made to the tokens file.{Style.RESET_ALL}")
            else:
                print(f"\n{Fore.GREEN}No duplicate tokens found in {tokens_file}.{Style.RESET_ALL}")
            
            input(f"\n{Fore.YELLOW}Press Enter to continue...{Style.RESET_ALL}")
            
        elif choice == "4":
            print(f"\n{Fore.GREEN}Exiting...{Style.RESET_ALL}")
            break
        
        else:
            print(f"\n{Fore.RED}Invalid choice. Please try again.{Style.RESET_ALL}")
            input(f"\n{Fore.YELLOW}Press Enter to continue...{Style.RESET_ALL}")

if __name__ == "__main__":
    main()